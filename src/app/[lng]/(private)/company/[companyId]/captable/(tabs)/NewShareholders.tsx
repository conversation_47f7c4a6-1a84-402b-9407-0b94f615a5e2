import { FragmentOf, graphql, readFragment } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Table } from '@chakra-ui/react'

export const NewShareholdersFragment = graphql(`
  fragment NewShareholdersFragment on FullyDilutedRegister {
    newShareholders {
      commonShares {
        formatted
      }
      preferredShares {
        formatted
      }
      redeemableShares {
        formatted
      }
      id {
        type
        value
      }
      name
      totalShares {
        formatted
      }
      totalOwnership {
        formatted(maximumFractionDigits: 1)
      }
    }
  }
`)

export function NewShareholders(props: { data: FragmentOf<typeof NewShareholdersFragment> }) {
  const { t, lng } = useEbanaLocale()

  const data = readFragment(NewShareholdersFragment, props.data)

  const dataRows = data?.newShareholders.map(
    ({ id, name, commonShares, preferredShares, redeemableShares, totalOwnership }) => {
      return (
        <Table.Row key={id.value}>
          <Table.Cell h='3.6875rem'>{name}</Table.Cell>
          <Table.Cell>{commonShares.formatted}</Table.Cell>
          <Table.Cell>{preferredShares.formatted}</Table.Cell>
          <Table.Cell>{redeemableShares.formatted}</Table.Cell>
          <Table.Cell>{totalOwnership.formatted}</Table.Cell>
        </Table.Row>
      )
    }
  )

  return (
    <Box
      maxW='100%'
      overflowX='scroll'
      css={{
        md: {
          '&::-webkit-scrollbar': {
            display: 'none',
          },
          '&::MsOverflowStyle': 'none',
          scrollbarWidth: 'none',
        },
      }}>
      <Table.ScrollArea minW={{ base: '1440px', md: 'unset' }}>
        <Table.Root
          style={{
            borderSpacing: '0 5px',
          }}>
          <Table.Header>
            <Table.Row>
              <Table.ColumnHeader h='3.6875rem'>{t('name')}</Table.ColumnHeader>
              <Table.ColumnHeader>{t('common_shares')}</Table.ColumnHeader>
              <Table.ColumnHeader>{t('preffered_shares')}</Table.ColumnHeader>
              <Table.ColumnHeader>{t('common_shares')}</Table.ColumnHeader>
              <Table.ColumnHeader>{t('total_shares')}</Table.ColumnHeader>
            </Table.Row>
          </Table.Header>
          <Table.Body>{dataRows}</Table.Body>
        </Table.Root>
      </Table.ScrollArea>
    </Box>
  )
}
