import PieChartWithList from '@/components/EbPieChart/PieChartWithList'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box } from '@chakra-ui/react'

export const ShareClassesFragment = graphql(`
  fragment ShareClassesFragment on FullyDilutedRegister {
    breakdown {
      name
      amount {
        p {
          formatted(maximumFractionDigits: 3)
          value
        }
      }
    }
  }
`)

export function ShareClasses(props: { data: FragmentOf<typeof ShareClassesFragment> }) {
  const { t, lng } = useEbanaLocale()

  const data = readFragment(ShareClassesFragment, props.data)

  const renderedShareholders = data?.breakdown.map((category) => {
    return {
      label: category.name,
      value: category.amount.p.value,
      displayedValue: category.amount.p.formatted,
    }
  })

  return (
    <Box
      width={{ base: '100%', md: '50%' }}
      p='2em'
      pt='1em'
      borderRadius='14px'
      bg='background.container'
      borderWidth='1px'
      borderStyle='solid'
      borderColor='border.container'>
      <PieChartWithList
        title={t('share_categories')}
        data={renderedShareholders}
        dark={false}
        colors={undefined}
        customTitle={undefined}
        customValue={undefined}
        listTitle={undefined}
        listValue={undefined}
      />
    </Box>
  )
}
