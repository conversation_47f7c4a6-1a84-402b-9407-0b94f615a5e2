import PieChartWithList from '@/components/EbPieChart/PieChartWithList'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box } from '@chakra-ui/react'

export const Top5ShareholdersFragment = graphql(`
  fragment Top5ShareholdersFragment on ShareholderRegisterTop5 {
    shareholders {
      name
      totalOwnership {
        value
        formatted(maximumFractionDigits: 1)
      }
    }

    ownership {
      complement
      complementFormatted(maximumFractionDigits: 1)
    }
  }
`)

export function Top5Shareholders(props: { data: FragmentOf<typeof Top5ShareholdersFragment> }) {
  const { t, lng } = useEbanaLocale()

  const data = readFragment(Top5ShareholdersFragment, props.data)

  const renderedShareholders = data?.shareholders?.map((shareholder) => {
    return {
      label: shareholder.name,
      value: shareholder.totalOwnership.value,
      displayedValue: shareholder.totalOwnership.formatted,
    }
  })

  const renderedData = renderedShareholders && [
    ...renderedShareholders,
    {
      label: t('others'),
      value: data?.ownership.complement,
      displayedValue: data?.ownership.complementFormatted,
    },
  ]

  return (
    <Box
      width={{ base: '100%', md: '50%' }}
      p='2em'
      pt='1em'
      borderRadius='14px'
      bg='background.container'
      borderWidth='1px'
      borderStyle='solid'
      borderColor='border.container'>
      <PieChartWithList title={t('cap_table_summary')} data={renderedData} />
    </Box>
  )
}
