import { Switch } from '@/components/ui/switch'
import { Tooltip } from '@/components/ui/tooltip'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Flex, Image, List, Stack } from '@chakra-ui/react'
import UpdateCapTableBtn from '../../update'
import { useSimulation } from '../simulation-wrapper'
import { IssuedShares } from './IssuedShares'
import { TotalCapitalContribution } from './TotalCapitalContribution'
import { TotalShareholders } from './TotalShareholders'

export const OverviewFragment = graphql(`
  fragment OverviewFragment on CompanyManagement {
    fullyDiluted {
      capital {
        issuedShares {
          formatted
        }
        issuedAmount {
          formatted
        }
      }
      totalShareholders {
        formatted
      }
    }
    register {
      capital {
        issuedShares {
          formatted
        }
        issuedAmount {
          formatted
        }
      }
      totalShareholders {
        formatted
      }
    }
  }
`)

export default function Overview({ data }: { data: FragmentOf<typeof OverviewFragment> }) {
  const { t, lng } = useEbanaLocale()

  const { simulation, toggleSimulation } = useSimulation()

  const result = readFragment(OverviewFragment, data)

  const includeSafeTooltipElement = (
    <Box
      p='0.75em'
      boxShadow='0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08)'
      color='white'>
      <Box mb='0.5em' fontWeight={600}>
        {t('safe_note_tooltip_title')}
      </Box>
      <List.Root>
        <List.Item>
          <Box fontWeight={500}>{t('safe_note_tooltip_description_first')}</Box>
        </List.Item>
        <li>
          <Box fontWeight={500}>{t('safe_note_tooltip_description_second')}</Box>
        </li>
        <li>
          <Box fontWeight={500}>{t('safe_note_tooltip_description_third')}</Box>
        </li>
      </List.Root>
    </Box>
  )

  return (
    <Box
      as='section'
      background='background.container'
      py='1.5625em'
      px='1.875em'
      borderRadius='14px'
      borderTopStartRadius={0}
      borderTopEndRadius={0}
      borderWidth='1px'
      borderStyle='solid'
      borderColor='border.container'
      position='relative'>
      <Flex
        bg='background.box'
        borderWidth='1px'
        borderStyle='solid'
        borderColor='border.box'
        p='0.75em'
        pe='1.69em'
        borderRadius='12px'
        alignItems='center'
        justifyContent='space-between'
        mb='1.87em'
        mt='0.62em'>
        <Flex gap='0.75em'>
          <Flex width='3em' height='3em' borderRadius='50px' alignItems='center' justifyContent='center' flexShrink='0'>
            <Image src='/assets/logo/sm/light.svg' width='3em' height='3em' alt='!' />
          </Flex>
          <Stack>
            <Flex gap='0.38em' alignItems='center'>
              <Box textStyle='h5' fontWeight={700}>
                {t('include_safe_note_title')}
              </Box>
              <Tooltip
                showArrow={true}
                content={includeSafeTooltipElement}
                positioning={{ placement: 'top' }}
                contentProps={{
                  background: '#101828',
                  borderRadius: '8px',
                }}>
                <Image src='/assets/info.svg' width='1em' height='1em' alt='!' />
              </Tooltip>
            </Flex>
            <Box textStyle='body4' fontWeight={500}>
              {t('include_safe_note_subtitle')}
            </Box>
          </Stack>
        </Flex>
        {/* {data.fullyDiluted != null && ( */}
        <Switch
          id='add_safe'
          checked={simulation}
          onCheckedChange={() => {
            toggleSimulation()
          }}
        />
        {/* )} */}
      </Flex>
      <Box as='h5' textStyle='h4' color={simulation ? 'white' : 'heading'} fontWeight={700} my='0.9375em'>
        <Flex fontSize='1.3em' justifyContent='space-between' alignItems='center'>
          {t('overview')}
          <UpdateCapTableBtn />
        </Flex>
      </Box>
      <Flex direction={{ base: 'column', md: 'row' }} gap='1.13em' mb='0.62em'>
        {/* <TotalFullyDiutedShares data={data} /> */}
        <IssuedShares
          issuedShares={
            simulation
              ? result?.fullyDiluted.capital.issuedShares.formatted
              : result.register.capital.issuedShares.formatted
          }
        />
        <TotalCapitalContribution
          issuedAmount={
            simulation
              ? result?.fullyDiluted.capital.issuedAmount.formatted
              : result.register.capital.issuedAmount.formatted
          }
        />
        <TotalShareholders
          totalShareholders={
            simulation ? result?.fullyDiluted.totalShareholders.formatted : result.register.totalShareholders.formatted
          }
        />
      </Flex>
    </Box>
  )
}
