import { RiyalNewSignIcon } from '@/components/EbanaIcons'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Stack } from '@chakra-ui/react'

export function TotalCapitalContribution(props: { issuedAmount: string }) {
  const { t } = useEbanaLocale()


  return (
    <Stack
      background='background.box'
      justifyContent='space-between'
      borderWidth='1px'
      borderStyle='solid'
      borderColor='border.box'
      py='1.5em'
      px='1.46em'
      borderRadius='14px'
      flex='100%'>
      <Box color='text.typo3'>{t('total_capital_contribution')}</Box>
      <Box
        dir='ltr'
        width='fit-content'
        textStyle='h4'
        fontWeight={700}
        // color={colorMode === 'dark' ? 'white' : '#323B41'}>
        color='text.typo1'>
        <RiyalNewSignIcon fill='text.typo1' /> {props.issuedAmount.replace('SAR', '').replace('﷼', '')}
      </Box>
    </Stack>
  )
}
