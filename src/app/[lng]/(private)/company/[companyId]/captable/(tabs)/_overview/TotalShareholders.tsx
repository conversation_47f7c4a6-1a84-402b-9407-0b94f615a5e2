import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Stack } from '@chakra-ui/react'

export function TotalShareholders(props: { totalShareholders: string }) {
  const { t  } = useEbanaLocale()

  return (
    <Stack
      background='background.box'
      justifyContent='space-between'
      borderWidth='1px'
      borderStyle='solid'
      borderColor='border.box'
      py='1.5em'
      px='1.46em'
      borderRadius='14px'
      flex='100%'>
      <Box color='text.typo3'>{t('total_shareholders')}</Box>
      {/* <Box textStyle='h4' fontWeight={700} color={colorMode === 'dark' ? 'white' : '#323B41'}> */}
      <Box textStyle='h4' fontWeight={700} color='text.typo1'>
        {props.totalShareholders}
      </Box>
    </Stack>
  )
}
