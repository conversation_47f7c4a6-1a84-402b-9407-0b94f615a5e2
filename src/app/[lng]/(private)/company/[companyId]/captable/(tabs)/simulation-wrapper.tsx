import { usePathname } from 'next/navigation'
import React from 'react'

export function SimulationWrapper({ children }: { children: React.ReactElement }) {
  const [simulation, setSimulation] = React.useState(false)
  const pathname = usePathname()

  React.useEffect(
    function disableSimulationOnNavigation() {
      setSimulation(false)
    },
    [pathname]
  )

  return (
    <SimulationContext.Provider
      value={{
        simulation: false,
        toggleSimulation: () => {
          // setSimulation((prev) => !prev)
          setSimulation(false)
        },
      }}>
      {/* <ColorModeProvider forcedTheme={simulation ? 'dark' : 'light'}> */}
      {/* <Theme appearance={simulation ? 'dark' : 'light'}>{children}</Theme> */}
      {children}
      {/* </ColorModeProvider> */}
    </SimulationContext.Provider>
  )
}

export function useSimulation(): SimulationContextType {
  return React.useContext(SimulationContext)
}

type SimulationContextType = {
  simulation: boolean
  toggleSimulation: VoidFunction
}

const SimulationContext = React.createContext<SimulationContextType>(null)
