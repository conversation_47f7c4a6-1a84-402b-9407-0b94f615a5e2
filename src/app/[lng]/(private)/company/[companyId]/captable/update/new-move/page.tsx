'use client'

import Button<PERSON>ith<PERSON>lert from '@/components/buttons/ButtonWithAlert'
import { CheckIcon, PlusIcon } from '@/components/EbanaIcons'
import { File } from '@/components/file'
import FileDrag from '@/components/FileDrag'
import { EbanaNumberField, EbanaSelectField, EbanaTextField } from '@/components/form'
import { EbanaComboboxField2 } from '@/components/form/EbanaComboboxField2'
import EbanaFileController from '@/components/form/EbanaFileController'
import { EbanaForm } from '@/components/form/EbanaForm'
import { EbanaTextAreaField } from '@/components/form/EbanaTextAreaField'
import FormLoading from '@/components/loading/Form'
import { RenderWhenFetched } from '@/components/RenderIf'
import { DialogBody, DialogContent, DialogFooter, DialogHeader, DialogRoot } from '@/components/ui/dialog'
import { CompanyContext } from '@/context/company'
import { graphql, ResultOf, VariablesOf } from '@/graphql'
import {
  ComboBoxFieldFragment,
  FileFieldFragment,
  FormFragment,
  NumberFieldFragment,
  SelectFieldFragment,
  TextFieldFragment,
} from '@/graphql/fragments'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Button, Flex, Image, Stack, Table, Tabs } from '@chakra-ui/react'
import NextLink from 'next/link'
import { useRouter } from 'next/navigation'
import { route } from 'nextjs-routes'
import React, { useEffect } from 'react'
import { Control, useFieldArray, useForm, UseFormReturn } from 'react-hook-form'
import { IoMdClose } from 'react-icons/io'
import { useMutation, UseMutationExecute, useQuery } from 'urql'

const NewMovePageQuery = graphql(
  `
    query NewMovePageQuery($id: ID!) {
      company(id: $id) {
        register {
          batchMoveStockForm {
            ...FormFragment
            note {
              ...TextFieldFragment
            }
            document {
              ...FileFieldFragment
            }
            moves {
              path
              defaultValues
            }
            moves_pricePerShare {
              ...NumberFieldFragment
            }
            moves_from {
              ...ComboBoxFieldFragment
            }
            moves_toId_value {
              ...TextFieldFragment
            }
            moves_toId_type {
              ...SelectFieldFragment
            }
            moves_toName {
              ...TextFieldFragment
            }
            moves_toNationality {
              path
              label
              placeholder
              available
              required
              requiredMessage
              status
              statusMessage
              multiselect
              kind
              options {
                label
                value
                flagUnicode
                callingCode
              }
            }
            moves_shareCount {
              ...NumberFieldFragment
            }
            moves_shareClass {
              path
              label
              placeholder
              available
              required
              requiredMessage
              status
              statusMessage
              multiselect
              kind
              options {
                prefix
                name
              }
            }
            moves_toExisting {
              ...ComboBoxFieldFragment
            }
          }
        }
      }
    }
  `,
  [FormFragment, TextFieldFragment, FileFieldFragment, SelectFieldFragment, NumberFieldFragment, ComboBoxFieldFragment]
)

export default function NewMovePage() {
  const { id } = React.useContext(CompanyContext)

  const [{ data }] = useQuery({
    query: NewMovePageQuery,
    variables: {
      id,
    },
  })

  return (
    <RenderWhenFetched
      loading={!data}
      LoadingComponent={<FormLoading />}
      render={() => {
        return <NewMoveForm form={data.company.register.batchMoveStockForm} />
      }}
    />
  )
}

type UseNestedFromReturn<T, R> = {
  defaultValues: any
  open: boolean
  edit(index: number | null): void
  save(item: T, more?: boolean | undefined, stepData?: any): void
  remove(index?: number | number[]): void
  cancel(): void
  fields: { field: T & { id: string }; additional: R }[]
  methods: UseFormReturn
}
type UseNestedFormParam = {
  path: string
  control: Control<any, any>
  defaultValue: any
}
function useNestedForm<T, R>(prop: UseNestedFormParam): UseNestedFromReturn<T, R> {
  const [open, setOpen] = React.useState<boolean>(false)
  const defaultValues = {
    [prop.path]: [prop.defaultValue],
  }

  const methods = useForm({ defaultValues })

  const { append, update, remove, fields } = useFieldArray({
    name: prop.path,
    control: prop.control,
  })
  const [active, setActive] = React.useState<number | null>(null)

  const [enriched, setEnriched] = React.useState<any>({ inflight: null })

  const edit = (index: number | null) => {
    if (index === null || index === undefined) {
      setActive(null)
      setOpen(true)
    } else {
      setActive(index)
      const { id, ...rest } = fields[index]
      methods.setValue(prop.path, [rest])
      setOpen(true)
    }
  }

  const save = (item: T, more: boolean, stepData: any) => {
    if (active === null || active === undefined) {
      append(item)
      setEnriched({ ...enriched, inflight: stepData })
    } else {
      update(active, item)
      const id = (item as any).id
      setEnriched({ ...enriched, [id]: stepData })
    }

    methods.reset()

    if (!more) setOpen(false)
  }

  const cancel = () => {
    methods.reset()
    setOpen(false)
  }

  if (prop.path.includes('_')) throw new Error('nested lists are not supported')

  useEffect(() => {
    if (enriched.inflight) {
      const id = fields[fields.length - 1].id
      setEnriched({ ...enriched, [id]: enriched.inflight, inflight: null })
    }
  }, [enriched, fields])

  return {
    defaultValues,
    open,
    edit,
    save,
    remove,
    cancel,
    fields: fields.map((it) => ({ field: it as T & { id: string }, additional: enriched[it.id] as R })),
    methods,
  }
}

const NewMoveMutation = graphql(`
  mutation NewMoveMutation($id: ID!, $input: RegisterBatchMoveStockInput!, $step: Int!) {
    registerBatchMoveStock(id: $id, input: $input, step: $step) {
      register {
        id
      }
      step10 {
        from {
          name
        }
        toExisting {
          name
        }
        toNewName
        shareClass {
          name
        }
        shareCount {
          formatted
        }
      }
    }
  }
`)
type FormType = ResultOf<typeof NewMovePageQuery>['company']['register']['batchMoveStockForm']

function NewMoveForm({ form }: { form: FormType }) {
  const { t, lng } = useEbanaLocale()
  const { id } = React.useContext(CompanyContext)

  const router = useRouter()

  const methods = useForm({ defaultValues: form.defaultValues })

  const shareholderForm = useNestedForm<
    VariablesOf<typeof NewMoveMutation>['input']['moves'][0],
    ResultOf<typeof NewMoveMutation>['registerBatchMoveStock']['step10']
  >({
    control: methods.control,
    path: form.moves.path,
    defaultValue: form.moves.defaultValues,
  })

  const [, mutation] = useMutation(NewMoveMutation)

  return (
    <EbanaForm
      onSuccessReplace={() =>
        route({
          pathname: '/[lng]/company/[companyId]/captable',
          query: { lng, companyId: id },
        })
      }
      step={1}
      methods={methods}
      form={form}
      mutation={mutation}
      alignContent='center'
      maxW='50em'
      mx='auto'
      mt='5em'>
      <Stack gap='3em' mb='3em'>
        <Flex position='relative' alignItems='center' justifyContent='space-between'>
          <Box textStyle='h2' fontWeight={700} color='typography.100'>
            {t('stock_move_title')}
          </Box>
          <Button
            _hover={{
              bg: 'background.200',
            }}
            bg='transparent'
            p='0.75rem'
            borderRadius='8px'
            px={0}
            width='fit-content'
            onClick={() => {
              router.replace(route({ pathname: '/[lng]/company/[companyId]/captable', query: { lng, companyId: id } }))
            }}
            me='1rem'>
            <IoMdClose color='#4E5D78' />
          </Button>
        </Flex>

        <Box bg='primary.100' borderRadius='14px' p='1em' textStyle='body3' color='#fff' fontWeight={500}>
          <Flex mt='0.5em' height='fit-content'>
            <Box minH='3.5em' minW='3.5em'>
              <Image src='/assets/ebana-dark-logo.svg' height='3.5em' alt='hi' />
            </Box>
            <Box>{t('stock_move-info-msg')}</Box>
          </Flex>
        </Box>
      </Stack>
      <Stack gap='1.875em'>
        <Stack>
          <Box textStyle='body4' fontWeight={500} color='typography.300'>
            {form.document.label}
          </Box>
          <EbanaFileController
            field={form.document}
            flProps={{ hidden: true }}
            render={({ append, remove, uploadState }) => (
              <>
                <FileDrag onChange={append} name='document' fileTypes={['application/pdf']} maxFileSize={10} onSizeError={null} />

                {uploadState.map(({ name }, index) => (
                  <File key={index} name={name} mt='1em'>
                    <Button key={index} aria-label='button' onClick={() => remove(index)} variant='plain'>
                      <Image src='/assets/delete.svg' width='100%' height='1.6rem' alt='delete' />
                    </Button>
                  </File>
                ))}
              </>
            )}
          />
        </Stack>
        <EbanaTextAreaField field={form.note} />
        {shareholderForm.fields.length > 0 && (
          <Flex alignItems='center' gap='2em' mb='0.5em' justifyContent='space-between'>
            <Box textStyle='h4' fontWeight={700}>
              {t('moves_list')}
            </Box>
          </Flex>
        )}

        <MoveForm
          form={form}
          mutation={mutation}
          methods={shareholderForm.methods}
          save={shareholderForm.save}
          cancel={shareholderForm.cancel}
          open={shareholderForm.open}
        />
      </Stack>
      <Stack gap='2em'>
        {shareholderForm.fields.length > 0 && (
          <Box bg='background.200' borderRadius='14px' px='0.25em'>
            <Box
              maxW='100%'
              overflowX='scroll'
              css={{
                md: {
                  '&::-webkit-scrollbar': {
                    display: 'none',
                  },
                  '&::MsOverflowStyle': 'none',
                  scrollbarWidth: 'none',
                },
              }}>
              <Table.ScrollArea minW={{ base: '1440px', md: 'unset' }}>
                <Table.Root>
                  <Table.Header>
                    <Table.Row>
                      <Table.ColumnHeader>{t('from')}</Table.ColumnHeader>
                      <Table.ColumnHeader>{t('to')}</Table.ColumnHeader>
                      <Table.ColumnHeader>{t('number_of_stocks')}</Table.ColumnHeader>
                      <Table.ColumnHeader>{t('share-class')}</Table.ColumnHeader>
                      <Table.ColumnHeader>{t('action')}</Table.ColumnHeader>
                    </Table.Row>
                  </Table.Header>
                  <Table.Body>
                    {shareholderForm.fields.map(({ field: move, additional }, index) => {
                      return (
                        <Table.Row key={move.id}>
                          <Table.Cell>{additional?.from?.name}</Table.Cell>
                          <Table.Cell>
                            {additional?.toExisting ? additional?.toExisting?.name : additional?.toNewName}
                          </Table.Cell>
                          <Table.Cell>{move.shareCount}</Table.Cell>
                          <Table.Cell>{additional?.shareClass?.name}</Table.Cell>
                          <Table.Cell>
                            <Flex gap='1.5em'>
                              <Button variant='plain' color='primary.200' onClick={() => shareholderForm.edit(index)}>
                                {t('edit')}
                              </Button>
                              <ButtonWithAlert
                                title={t('delete_stock_move_title')}
                                variant='plain'
                                actionVariant='danger'
                                color='primary.400'
                                onConfirm={() => shareholderForm.remove(index)}>
                                {t('delete')}
                              </ButtonWithAlert>
                            </Flex>
                          </Table.Cell>
                        </Table.Row>
                      )
                    })}
                  </Table.Body>
                </Table.Root>
              </Table.ScrollArea>
            </Box>
          </Box>
        )}

        <Flex gap='1em' justifyContent='space-between' flexWrap='wrap' pt='1.585625em'>
          <Button asChild variant='secondary'>
            <NextLink
              href={route({
                pathname: '/[lng]/company/[companyId]/captable',
                query: { lng, companyId: id },
              })}>
              {t('cancel')}
            </NextLink>
          </Button>
          <Flex justifyContent='end' gap='1.25em'>
            <Button variant='secondary' onClick={() => shareholderForm.edit(null)}>
              <PlusIcon h='12px' w='12px' stroke='primary.200'/>
              {t('add_move')}
            </Button>
            <Button disabled={shareholderForm?.fields?.length === 0} alignSelf='start' type='submit'>
              {t('execute_new_stock_moves')}
            </Button>
          </Flex>
        </Flex>
      </Stack>
    </EbanaForm>
  )
}

function MoveForm({
  form,
  mutation,
  methods,
  save,
  cancel,
  open,
}: {
  form: FormType
  mutation: UseMutationExecute
  methods: UseFormReturn
  save: (item, more, stepData) => void
  cancel: () => void
  open: boolean
}) {
  const { t } = useEbanaLocale()

  const [existingTo, setExistingTo] = React.useState(false)

  const [search, setSearch] = React.useState<string>()

  const { id } = React.useContext(CompanyContext)

  const [{ data }] = useQuery({
    query: ShareholderOptionsQuery,
    variables: {
      id,
      search: !search ? null : search,
    },
  })

  const options = data?.company.register?.shareholder_options ?? []

  function onSubmit(res) {
    const data = methods.getValues().moves[0]
    if (existingTo) {
      save(
        {
          toExisting: data.toExisting,
          from: data.from,
          shareCount: data.shareCount,
          shareClass: data.shareClass,
        },
        false,
        res.registerBatchMoveStock?.step10
      )
    } else {
      save(
        {
          ...data,
          toExisting: null,
        },
        false,
        res.registerBatchMoveStock?.step10
      )
    }
  }

  return (
    <>
      <DialogRoot open={open} size='full'>
        <DialogContent>
          <Stack w='50em' mx='auto' mt='5vh'>
            <DialogHeader>{t('new_stock_move')}</DialogHeader>
            <EbanaForm
              form={form}
              mutation={mutation}
              skipSuccessToast={true}
              methods={methods}
              step={10}
              onSuccess={onSubmit}>
              <DialogBody>
                <Stack gap='4em'>
                  <Stack gap='2em'>
                    <Box textStyle='h4' fontWeight={700}>
                      {t('stock_move_from')}
                    </Box>
                    <Box mt='-1em' p='2em' borderWidth='1px' borderRadius='14px' borderColor='stroke.100'>
                      <Stack gap='1.5em'>
                        <Flex gap='2em' alignItems='center'>
                          <EbanaComboboxField2
                            field={form.moves_from}
                            indices={[0]}
                            options={options.map((it) => ({
                              label: it.name,
                              value: it.id.encoded,
                            }))}
                            onInputChange={(v) => setSearch(v)}
                            filterOption={() => true}
                          />
                        </Flex>
                        <Flex gap='2em' alignItems='center'>
                          <Flex minWidth='48%' gap='2em' alignItems='center'>
                            <EbanaNumberField field={form.moves_shareCount} indices={[0]} />
                            <EbanaNumberField field={form.moves_pricePerShare} indices={[0]} />
                          </Flex>
                          <EbanaComboboxField2
                            field={form.moves_shareClass}
                            indices={[0]}
                            options={form.moves_shareClass.options.map(({ prefix, name }) => ({
                              label: name,
                              value: prefix,
                            }))}
                          />
                        </Flex>
                      </Stack>
                    </Box>
                  </Stack>
                  <Stack gap='2em'>
                    <Box textStyle='h4' fontWeight={700}>
                      {t('stock_move_to')}
                    </Box>
                    <Box mt='-1em' p='2em' borderWidth='1px' borderRadius='14px' borderColor='stroke.100'>
                      <Tabs.Root
                        value={String(existingTo ? 1 : 0)}
                        onValueChange={({ value }) => setExistingTo(value !== '0')}
                        display='flex'
                        flexDirection='column'
                        gap='2em'>
                        <Tabs.List
                          borderBottomStyle='solid'
                          borderBottomWidth='1px !important'
                          borderBottomColor='stroke.100 !important'>
                          <Tabs.Trigger
                            value='0'
                            flex='100%'
                            _after={{
                              content: "''",
                              position: 'absolute',
                              border: 'none',
                              bottom: 0,
                              left: 0,
                              right: 0,
                              height: '4px',
                              background: existingTo ? 'transparent' : 'primary.200',
                              borderTopStartRadius: '12px',
                              borderTopEndRadius: '12px',
                            }}>
                            <Flex alignItems='center' gap='0.8em' justifyContent='center' pb='0.5em'>
                              <Box width='22px'>{!existingTo && <CheckIcon />}</Box>
                              <Box
                                textStyle='body3'
                                fontWeight={500}
                                color={existingTo ? 'typography.300' : 'typography.100'}>
                                {t('non_existent_shareholder')}
                              </Box>
                            </Flex>
                          </Tabs.Trigger>
                          <Tabs.Trigger
                            value='1'
                            flex='100%'
                            _after={{
                              content: "''",
                              position: 'absolute',
                              border: 'none',
                              bottom: 0,
                              left: 0,
                              right: 0,
                              height: '4px',
                              background: existingTo ? 'primary.200' : 'transparent',
                              borderTopStartRadius: '12px',
                              borderTopEndRadius: '12px',
                            }}>
                            <Flex alignItems='center' gap='0.8em' justifyContent='center' pb='0.5em'>
                              <Box width='22px'>{existingTo && <CheckIcon />}</Box>
                              <Box
                                textStyle='body3'
                                fontWeight={500}
                                color={existingTo ? 'typography.100' : 'typography.300'}>
                                {t('existing_shareholder')}
                              </Box>
                            </Flex>
                          </Tabs.Trigger>
                        </Tabs.List>
                        <Tabs.Content value='0' p={0}>
                          <Stack gap='1.5em'>
                            <Flex gap='2em' alignItems='center'>
                              <EbanaTextField field={form.moves_toId_value} indices={[0]} />
                              <EbanaSelectField field={form.moves_toId_type} indices={[0]} />
                            </Flex>
                            <Flex gap='2em' alignItems='center'>
                              <EbanaTextField field={form.moves_toName} indices={[0]} />
                              <EbanaComboboxField2
                                field={form.moves_toNationality}
                                indices={[0]}
                                options={form.moves_toNationality.options.map(({ label, value }) => ({
                                  label,
                                  value,
                                }))}
                              />
                            </Flex>
                          </Stack>
                        </Tabs.Content>
                        <Tabs.Content value='1' p={1}>
                          <AddExistingShareholder form={form} />
                        </Tabs.Content>
                      </Tabs.Root>
                    </Box>
                  </Stack>
                </Stack>
              </DialogBody>
              <DialogFooter mt='20em' justifyContent='space-between' gap='2em'>
                <Button variant='secondary' onClick={cancel}>
                  {t('cancel')}
                </Button>
                <Button type='submit'>{t('save')}</Button>
              </DialogFooter>
            </EbanaForm>
          </Stack>
        </DialogContent>
      </DialogRoot>
    </>
  )
}

const ShareholderOptionsQuery = graphql(`
  query ShareholderOptionsQuery($id: ID!, $search: String) {
    company(id: $id) {
      register {
        shareholder_options(search: $search) {
          name
          id {
            value
            type
            encoded
          }
        }
      }
    }
  }
`)
function AddExistingShareholder({ form }: { form: FormType }) {
  const [search, setSearch] = React.useState<string>()

  const { id } = React.useContext(CompanyContext)

  const [{ data }] = useQuery({
    query: ShareholderOptionsQuery,
    variables: {
      id,
      search: !search ? null : search,
    },
  })

  const options = data?.company.register?.shareholder_options ?? []
  return (
    <EbanaComboboxField2
      field={form.moves_toExisting}
      indices={[0]}
      options={options.map((it) => ({
        label: it.name,
        value: it.id.encoded,
      }))}
    />
  )
}
