'use client'

import Card<PERSON>ontainer from '@/components/CardContainer'
import { GqlData } from '@/components/GqlData'
import NavigationContainer from '@/components/Navigation'
import PageContainer from '@/components/pageContainer/PageContainer'
import { useAuth } from '@/context/new-auth'
import { PressReleaseDetails } from '@/features/press-release-details'
import { graphql } from '@/graphql'
import { use } from 'react'
import { useQuery } from 'urql'

const CompanyAnnouncementPageQuery = graphql(
  `
    query CompanyAnnouncementPageQuery($id: ID!, $pressReleaseId: ID!) {
      company(id: $id) {
        pressRelease(id: $pressReleaseId) {
          ...PressReleaseDetailsFragment
        }
      }
    }
  `,
  [PressReleaseDetails.fragment]
)

export default function CompanyAnnouncementPage(props) {
  const params = use(props.params)

  const { id } = params as any

  const { activeWorkspace } = useAuth()

  const response = useQuery({
    query: CompanyAnnouncementPageQuery,
    variables: {
      id: activeWorkspace.id,
      pressReleaseId: id,
    },
  })
  const [{ data }] = response

  return (
    <NavigationContainer>
      <PageContainer>
        <CardContainer>
          <GqlData
            response={response}
            render={() => {
              return <PressReleaseDetails data={data.company.pressRelease} />
            }}
          />
        </CardContainer>
      </PageContainer>
    </NavigationContainer>
  )
}
