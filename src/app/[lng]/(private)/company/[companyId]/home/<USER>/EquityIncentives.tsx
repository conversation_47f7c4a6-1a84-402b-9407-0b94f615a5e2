import React, { useRef } from 'react';
import {
    Box,
    Button,
    Flex,
    Image,
    Input,
    VStack
} from '@chakra-ui/react';
import { ResponsiveContainer } from 'recharts';
import { IoChevronDownCircleOutline } from 'react-icons/io5';
import PieChartContainer from '@/components/EbPieChart';
import { useRouter } from 'next/navigation';
import { route } from 'nextjs-routes'
import { CompanyContext } from '@/context/company';
import { useEbanaLocale } from '@/hooks/useEbanaTranslations';
import { SearchIcon } from '@/components/EbanaIcons';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { FragmentOf, graphql } from '@/graphql'


EquityIncentivesDashboard.fragment = graphql(`
    fragment EquityIncentivesDashboard on EquityIncentiveCompanyReports {
      # Total stocks
      totalShareCount {
        formatted
        value
      }
      # Contracts
      grantsCount {
        formatted
        value
      }
      # Grantees
      granteeCount {
        formatted
        value
      }
      programs {
        id
        # Granted
        grantedShares {
          ...NumberToTotal
        }
        # Available
        availableShares {
          ...NumberToTotal
        }
        # Completed
        finishedGrants {
          ...NumberToTotal
        }
        # Active
        activeGrants {
          ...NumberToTotal
        }
        # Issued
        pendingGrants {
          ...NumberToTotal
        }
      }
    }
    fragment NumberToTotal on NumberToTotal {
      n {
        value
        formatted
      }
      p {
        value
        formatted
      }
    }
  `)

export function EquityIncentivesDashboard({ data }: { data: FragmentOf<typeof EquityIncentivesDashboard.fragment> }) {
    const [showSearch, setShowSearch] = React.useState(false)
    const dropDownRef = useRef(null)

    useOnClickOutside(dropDownRef, () => setShowSearch(false))

    const { t, lng } = useEbanaLocale()

    const router = useRouter()
    const { id: companyId } = React.useContext(CompanyContext)

    const query = graphql(`
        query Compensations($id: ID!) {
          company(id: $id) {
            equityIncentiveReports {
              financialReportXlsx {
                url
              }
            }
          }
        }
      `)

    const stockData = [
        { name: 'Granted', value: 25, count: 1870, color: '#6366F1' },
        { name: 'Available', value: 25, count: 1870, color: '#10B981' },
        { name: 'Unused', value: 50, count: 0, color: '#F3F4F6' }
    ];

    const contractData = [
        { name: 'Active', value: 86.7, count: 26, color: '#10B981' },
        { name: 'Completed', value: 13.3, count: 4, color: '#F59E0B' },
    ];



    return (
        <Box bg="white" borderRadius="14px" border="1px" borderColor="gray.200" maxW="800px">
            <Flex p={4} justify="space-between" align="center" mb={6} borderBottom='1px solid #F3F4F6'>
                <Flex position='relative'>
                    <Flex gap={2} alignItems='center' onClick={() => setShowSearch(!showSearch)} cursor='pointer'>
                        <Box fontSize="lg" fontWeight="semibold">
                            All Equity Incentives
                        </Box>
                        <IoChevronDownCircleOutline size='1.5em' />
                    </Flex>

                    {
                        showSearch &&
                        <Box ref={dropDownRef} position='absolute' width='14rem' height='15rem' bg='#fff' top='3rem' shadow='md' borderRadius='sm' zIndex={1000}>
                            <Flex alignItems='center' px={2} borderBottom='1px solid #F3F4F6'>
                                <SearchIcon />
                                <Input placeholder='Search program...' border='none' focusRing='none' />
                            </Flex>
                            <Box overflowY='scroll'>
                                <Box p={2} fontWeight='medium' _hover={{ bg: '#F3F4F6' }} cursor='pointer'>
                                    All Equity Incentives
                                </Box>
                                <Box p={2} fontWeight='medium' _hover={{ bg: '#F3F4F6' }} cursor='pointer'>
                                    Program 1
                                </Box>
                            </Box>
                        </Box>
                    }
                </Flex>
                <Button
                    variant="outline"
                    size="sm"
                    borderRadius='14x'
                    color='primary.200'
                    borderColor='primary.200'
                    px='4'
                    borderWidth={2}
                    fontWeight='bold'
                    onClick={() => {
                        router.push(
                            route({ pathname: '/[lng]/company/[companyId]/equity-incentive-grants/management', query: { lng, companyId } })
                        )
                    }}
                >
                    See More details
                </Button>
            </Flex>

            <Flex justify="space-between" mb={8} p={4} flexWrap='wrap' gap={{ base: '2rem', md: 0 }}>
                <Flex flex={1} flexDirection='column'>
                    <StatCard
                        image='/assets/total_stocks.svg'
                        label="Total Stocks"
                        value={2000}
                        color="blue.500"
                    />
                    <Flex flex={1} align="start" gap={4} flexDirection='row' alignItems='center' mt={10}>
                        <DonutChart data={stockData} />
                        <Legend data={stockData} />
                    </Flex>
                </Flex>
                <Flex flex={1} flexDirection='column'>
                    <Flex justifyContent='space-between' flexWrap='wrap'>
                        <StatCard
                            image='/assets/contracts.svg'
                            label="Contracts"
                            value={30}
                            color="blue.500"
                        />
                        <StatCard
                            image='/assets/grantees.svg'
                            label="Grantees"
                            value={1000}
                            color="blue.500"
                        />
                    </Flex>
                    <Flex flex={1} align="start" gap={4} flexDirection='row' alignItems='center' mt={10}>
                        <DonutChart data={contractData} />
                        <Legend data={contractData} />
                    </Flex>
                </Flex>
            </Flex>
        </Box>
    );
};

function StatCard({ image, label, value, color = 'blue.500' }) {
    return <Flex gap={2} align="start">
        <Flex gap={3}>
            <Image src={image} alt={label} width='3rem' height='3rem' />
            <VStack align="start" gap={0}>
                <Box fontSize="sm" color="gray.500">
                    {label}:
                </Box>
                <Box fontSize="2xl" fontWeight="bold">
                    {value.toLocaleString()}
                </Box>
            </VStack>
        </Flex>
    </Flex>
};

function DonutChart({ data, size = 120 }) {
    const chartColors = ['#003654', '#00BFB2']

    return <ResponsiveContainer width={size} height={size}>
        <PieChartContainer
            lng='ar'
            variant='thin'
            data={data}
            dataKey='value'
            colors={chartColors}
            innerRadius={50}
            outerRadius={60}
            showTotal={false}
        />
    </ResponsiveContainer>
};

function Legend({ data }) {
    return <VStack align="start" gap={2}>
        {data.map((item, index) => (
            item.count > 0 && (
                <Flex key={index} gap={2} alignItems='center'>
                    <Box w={3} h={3} borderRadius="full" bg={item.color} />
                    <Box fontSize="sm">
                        <Box as="span" fontWeight="medium">{item.name}</Box>
                        <Box as="span" color="gray.500" ml={1}>
                            {item.value}% ({item.count.toLocaleString()})
                        </Box>
                    </Box>
                </Flex>
            )
        ))}
    </VStack>
};

export default EquityIncentivesDashboard;