import CardContainer from '@/components/CardContainer'
import PieChartWithList from '@/components/EbPieChart/PieChartWithList'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, GridItem } from '@chakra-ui/react'

export const Top5ShareholdersFragment = graphql(`
  fragment Top5ShareholdersFragment on ShareholderRegister {
    totalShareholders {
      formatted
    }
    capital {
      issuedShares {
        formatted
      }
    }
    top5 {
      shareholders {
        name
        totalOwnership {
          value
          formatted(maximumFractionDigits: 1)
        }
      }

      ownership {
        complement
        complementFormatted(maximumFractionDigits: 1)
      }
    }
  }
`)

export default function Captable(props: { data: FragmentOf<typeof Top5ShareholdersFragment> }) {
  const { t } = useEbanaLocale()

  const { top5, capital, totalShareholders } = readFragment(Top5ShareholdersFragment, props.data)

  const renderedShareholders = top5?.shareholders?.map((shareholder) => {
    return {
      label: shareholder.name,
      value: shareholder.totalOwnership.value,
      displayedValue: shareholder.totalOwnership.formatted,
    }
  })

  const renderedData = renderedShareholders && [
    ...renderedShareholders,
    {
      label: t('others'),
      value: top5.ownership.complement,
      displayedValue: top5.ownership.complementFormatted,
    },
  ]

  return (
    <GridItem colSpan={2}>
      <CardContainer p='2em' pt='1em' borderRadius='14px' borderStyle='solid'>
        <Box mb='1em' fontSize='1.3em' fontWeight={700} color='#121423'>
          {t('cap_table_summary')}
        </Box>
        <Box mb='1.75em' height='1px' bg='gray.200' />
        <PieChartWithList
          title={t('cap_table_summary')}
          withTitle={false}
          data={renderedData}
          totalShareholders={capital.issuedShares.formatted}
          customTitle={t('total_stocks')}
          customValue={capital.issuedShares.formatted}
          listTitle={t('total_shareholders')}
          listValue={totalShareholders.formatted}
        />
      </CardContainer>
    </GridItem>
  )
}
