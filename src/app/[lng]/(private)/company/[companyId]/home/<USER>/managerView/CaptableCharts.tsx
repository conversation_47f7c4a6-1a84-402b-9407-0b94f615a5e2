import { useTranslation } from '@/app/i18n/client'
import CardContainer from '@/components/CardContainer'
import { ApplicationPropsContext } from '@/context/application-props'
import { CompanyContext } from '@/context/company'
import { Box, Button, Separator, Tabs, Text } from '@chakra-ui/react'
import { useRouter } from 'next/navigation'
import { route } from 'nextjs-routes'
import React, { useState } from 'react'
import ShareclassesChart from './charts/ShareclassesChart'
import ShareholdersChart from './charts/ShareholdersChart'

export default function CaptableCharts({ data }: { data: any }) {
  const { lng } = React.useContext(ApplicationPropsContext)
  const { t } = useTranslation(lng, 'Pages')

  const [value, setValue] = useState('1')
  const router = useRouter()

  const { id } = React.useContext(CompanyContext)

  return (
    <CardContainer py='2em' px={0} pt='1em' borderRadius='14px' borderStyle='solid'>
      <Box px='2em' mb='1em' display='flex' justifyContent='space-between' alignItems='center'>
        <Text fontSize='1.3em' fontWeight={700} color='#121423'>
          {t('cap_table_summary')}
        </Text>
        <Button
          onClick={() =>
            router.push(route({ pathname: '/[lng]/company/[companyId]/captable', query: { lng, companyId: id } }))
          }
          variant='secondary'
          px='1.5em'
          py='0.5em'
          _hover={{ bg: 'secondary.100' }}>
          {t('captable_tabs.see_more_details')}
        </Button>
      </Box>

      <Separator borderColor='gray.200' />

      <Tabs.Root value={value} onValueChange={({ value }) => setValue(value)} variant='plain' mt='18px'>
        <Box px='2em' py='.5em' display='flex' justifyContent='space-between' alignItems='center'>
          <Text textStyle='body3' fontWeight={600} alignSelf='center'>
            {value === '1' ? t('captable_tabs.shareholders_text') : t('captable_tabs.share_classes')}
          </Text>
          <Tabs.List bg='#F2F3F5' py='0.25rem' px='0.3em' borderRadius='full' gap={2} w='fit-content'>
            {[
              { label: t('captable_tabs.shareholders'), value: '1' },
              { label: t('captable_tabs.share_classes'), value: '2' },
            ].map(({ label, value: tabValue }) => (
              <Tabs.Trigger key={tabValue} value={tabValue} asChild _selected={{ bg: 'white', color: '#00263A' }}>
                <Button
                  px='1.5em'
                  py='0.5em'
                  borderRadius='full'
                  bg={value === tabValue ? 'white' : 'transparent'}
                  color={value === tabValue ? '#00263A' : 'gray.500'}
                  fontWeight='semibold'
                  _hover={{ bg: value === tabValue ? 'white' : 'gray.200' }}>
                  {label}
                </Button>
              </Tabs.Trigger>
            ))}
          </Tabs.List>
        </Box>

        <Box mx='2em'>
          <Tabs.Content value='1'>
            <ShareholdersChart data={data} />
          </Tabs.Content>
          <Tabs.Content value='2'>
            <ShareclassesChart data={data} />
          </Tabs.Content>
        </Box>
      </Tabs.Root>
    </CardContainer>
  )
}
