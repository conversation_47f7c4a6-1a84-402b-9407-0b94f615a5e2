import { useEffect, useRef } from 'react'

const NUM_PARTICLES = 2000

type Particle = {
  x: number
  y: number
  dx: number
  dy: number
  rotation: number
  shape: 'triangle' | 'spiral'
  color: string
}

function drawTriangle(ctx: CanvasRenderingContext2D, x: number, y: number, rotation: number, color: string) {
  ctx.save()
  ctx.translate(x, y)
  ctx.rotate(rotation)
  ctx.fillStyle = color
  ctx.beginPath()
  ctx.moveTo(0, 0)
  ctx.lineTo(5, 10)
  ctx.lineTo(10, 0)
  ctx.closePath()
  ctx.fill()
  ctx.restore()
}

function drawSpiral(ctx: CanvasRenderingContext2D, x: number, y: number, rotation: number, color: string) {
  ctx.save()
  ctx.translate(x, y)
  ctx.rotate(rotation)
  ctx.strokeStyle = color
  ctx.beginPath()
  for (let i = 0; i < 17; i++) {
    const angle = 0.35 * i
    const x2 = (0.2 + 1.5 * angle) * Math.cos(angle)
    const y2 = (0.2 + 1.5 * angle) * Math.sin(angle)
    ctx.lineTo(x2, y2)
  }
  ctx.stroke()
  ctx.restore()
}

export default function CustomConfetti() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const particlesRef = useRef<Particle[]>([])

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    canvas.width = window.innerWidth
    canvas.height = window.innerHeight

    // Create particles
    const shapes: ('triangle' | 'spiral')[] = ['triangle', 'spiral']
    const colors = ['#FFC700', '#00BFB2', '#7C3AED']
    particlesRef.current = Array.from({ length: NUM_PARTICLES }, () => ({
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height - canvas.height,
      dx: (Math.random() - 0.5) * 4,
      dy: Math.random() * 3 + 2,
      rotation: Math.random() * Math.PI,
      shape: shapes[Math.floor(Math.random() * shapes.length)],
      color: colors[Math.floor(Math.random() * colors.length)],
    }))

    let frameId: number

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      particlesRef.current.forEach(p => {
        p.x += p.dx
        p.y += p.dy
        p.rotation += 0.02

        if (p.shape === 'triangle') {
          drawTriangle(ctx, p.x, p.y, p.rotation, p.color)
        } else {
          drawSpiral(ctx, p.x, p.y, p.rotation, p.color)
        }
      })
      frameId = requestAnimationFrame(animate)
    }

    animate()

    return () => cancelAnimationFrame(frameId)
  }, [])

  return (
    <canvas
      ref={canvasRef}
      style={{ position: 'absolute', top: 0, left: 0, bottom: 0, right: 0, zIndex: 9999, pointerEvents: 'none' }}
    />
  )
}
