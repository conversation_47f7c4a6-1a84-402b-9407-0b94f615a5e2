import { useTranslation } from '@/app/i18n/client'
import {
  CompanyBoardIcon,
  CompanyCaptableIcon,
  CompanyCommitteeIcon,
  CompanyLogoIcon,
  CompanyPlanIcon,
  CompanyStampIcon,
} from '@/components/EbanaIcons'
import { ApplicationPropsContext } from '@/context/application-props'
import { CompanyContext } from '@/context/company'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { Box, Button, Card, Grid, GridItem, Heading, Link, SimpleGrid, Text } from '@chakra-ui/react'
import { route } from 'nextjs-routes'
import React from 'react'

EmptyStateCards.fragment = graphql(`
  fragment OnboardingProgress on CompanyOnboarding {
    logo
    stamp
    board
    capTable
    committees
    incentives
  }
`)

export default function EmptyStateCards({ data }: { data: FragmentOf<typeof EmptyStateCards.fragment> }) {
  const { lng } = React.useContext(ApplicationPropsContext)
  const { t } = useTranslation(lng, 'Pages')

  const { id } = React.useContext(CompanyContext)

  const { logo, stamp, board, capTable, committees, incentives } = readFragment(EmptyStateCards.fragment, data)

  const cards = [
    {
      view: capTable,
      id: 'capTable',
      title: t('setup.captable.title'),
      description: t('setup.captable.description'),
      actionLabel: t('setup.captable.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/captable/setup',
        query: { lng: lng, companyId: id },
      }),
      icon: <CompanyCaptableIcon />,
    },
    {
      view: logo,
      id: 'logo',
      title: t('setup.logo.title'),
      description: t('setup.logo.description'),
      actionLabel: t('setup.logo.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/profile/edit',
        query: { lng: lng, companyId: id },
      }),
      icon: <CompanyLogoIcon />,
    },
    {
      view: stamp,
      id: 'stamp',
      title: t('setup.stamp.title'),
      description: t('setup.stamp.description'),
      actionLabel: t('setup.stamp.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/profile/edit',
        query: { lng: lng, companyId: id },
      }),
      icon: <CompanyStampIcon />,
    },
    {
      view: board,
      id: 'board',
      title: t('setup.board.title'),
      description: t('setup.board.description'),
      actionLabel: t('setup.board.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/board/update',
        query: { lng: lng, companyId: id },
      }),
      icon: <CompanyBoardIcon />,
    },
    {
      view: committees,
      id: 'committees',
      title: t('setup.committee.title'),
      description: t('setup.committee.description'),
      actionLabel: t('setup.committee.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/committees/setup',
        query: { lng: lng, companyId: id },
      }),
      icon: <CompanyCommitteeIcon />,
    },
    {
      view: incentives,
      id: 'incentives',
      title: t('setup.plan.title'),
      description: t('setup.plan.description'),
      actionLabel: t('setup.plan.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/plan-management/plans',
        query: { lng: lng, companyId: id },
      }),
      icon: <CompanyPlanIcon />,
    },
  ]

  return (
    <SimpleGrid columns={{ base: 1, md: 2 }} gap={6} w='100%'>
      {cards.map((card) => (
        <Card.Root key={card.id} p={4} rounded='1rem'>
          <Grid h='full' templateColumns='repeat(12, 1fr)' gap={4}>
            <GridItem colSpan={10}>
              <Heading color='#00263A' as='h5' size='md' mb={3}>
                {card.title}
              </Heading>
              <Text fontSize='0.83rem;' lineHeight='1.5714285714' h='5rem' color='#4E5D78'>
                {card.description}
              </Text>
              <Link textDecoration='none' href={card.link}>
                <Button variant='outline' size='lg' color='#00263A'>
                  {card.actionLabel}
                </Button>
              </Link>
            </GridItem>
            <GridItem colSpan={2} position='relative' justifySelf='end'>
              <Box borderRadius='full' />
              {card.icon}
            </GridItem>
          </Grid>
        </Card.Root>
      ))}
    </SimpleGrid>
  )
}
