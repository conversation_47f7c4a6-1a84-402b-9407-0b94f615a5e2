import React, { useRef } from 'react';
import {
  Box,
  Button,
  Flex,
  Image,
  Input,
  VStack
} from '@chakra-ui/react';
import { ResponsiveContainer } from 'recharts';
import PieChartContainer from '@/components/EbPieChart';
import { useRouter } from 'next/navigation';
import { route } from 'nextjs-routes'
import { CompanyContext } from '@/context/company';
import { useEbanaLocale } from '@/hooks/useEbanaTranslations';
import { SearchIcon } from '@/components/EbanaIcons';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { FragmentOf, graphql } from '@/graphql'
import { PiCheckLight } from 'react-icons/pi';
import { FaChevronDown } from 'react-icons/fa';

EquityIncentivesDashboard.fragment = graphql(`
    fragment EquityIncentivesDashboard on EquityIncentiveCompanyReports {
      # Total stocks
      totalShareCount {
        formatted
        value
      }
      # Contracts
      grantsCount {
        formatted
        value
      }
      # Grantees
      granteeCount {
        formatted
        value
      }
      programs {
        id
        name
        # Granted
        grantedShares {
          ...NumberToTotal
        }
        # Available
        availableShares {
          ...NumberToTotal
        }
        # Completed
        finishedGrants {
          ...NumberToTotal
        }
        # Active
        activeGrants {
          ...NumberToTotal
        }
        # Issued
        pendingGrants {
          ...NumberToTotal
        }
      }
    }
    fragment NumberToTotal on NumberToTotal {
      n {
        value
        formatted
      }
      p {
        value
        formatted
      }
    },
  `)

export function EquityIncentivesDashboard(props) {
  console.log(props);
  return ;

  const [showSearch, setShowSearch] = React.useState(false)
  const [search, setSearch] = React.useState('')
  const [selectedProgramId, setSelectedProgramId] = React.useState(null)

  const dropDownRef = useRef(null)

  useOnClickOutside(dropDownRef, () => setShowSearch(false))

  const { t, lng } = useEbanaLocale()

  const router = useRouter()
  const { id: companyId } = React.useContext(CompanyContext)

  function selectProgramId(id: string | null) {
    setSelectedProgramId(id)
    setShowSearch(false)
  }

  const selectedProgram = selectedProgramId ? (data as any).programs.find((p) => p.id === selectedProgramId) : null

  const programs = search.length > 0 ?
    (data as any).programs.filter((p) => p.name.toLowerCase().includes(search.toLowerCase())) :
    (data as any).programs
    
  return (
    <Box bg="white" borderRadius="14px" border="1px" borderColor="gray.200" maxW="800px">
      <Flex p={4} justify="space-between" align="center" mb={6} borderBottom='1px solid #F3F4F6'>
        <Flex position='relative'>
          <Flex gap={2} alignItems='center' onClick={() => setShowSearch(!showSearch)} cursor='pointer'>
            <Box fontSize="lg" fontWeight="semibold">
              {t('all_equity_incentives')}
            </Box>
            <Box borderRadius='full' p={1} border='1px solid #F3F4F6'>
              <FaChevronDown size='1rem' />
            </Box>
          </Flex>

          {
            showSearch &&
            <Box ref={dropDownRef} position='absolute' width='14rem' height='15rem' bg='#fff' top='3rem' shadow='md' borderRadius='sm' zIndex={1000}>
              <Flex alignItems='center' px={2} borderBottom='1px solid #F3F4F6'>
                <SearchIcon />
                <Input placeholder='Search program...' border='none' focusRing='none'
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                />
              </Flex>
              <Box overflowY='scroll'>
                <Box
                  onClick={() => selectProgramId(null)}
                  p={2} fontWeight='medium' _hover={{ bg: '#F3F4F6' }} cursor='pointer' bg={selectedProgramId === null ? '#F3F4F6' : null}>
                  <Flex alignItems='center' justifyContent='space-between'>
                    <Box>{t('all_equity_incentives')}</Box>
                    {
                      selectedProgramId === null &&
                      <PiCheckLight fill='#00BFB2' />
                    }
                  </Flex>
                </Box>
                {
                  programs.map((program) => (
                    <Box
                      onClick={() => selectProgramId(program.id)}
                      key={program.id} p={2} fontWeight='medium' _hover={{ bg: '#F3F4F6' }} cursor='pointer' bg={selectedProgramId === program.id ? '#F3F4F6' : null}>
                      <Flex alignItems='center' justifyContent='space-between'>
                        <Box>{program.name}</Box>

                        {
                          selectedProgramId === program.id &&
                          <PiCheckLight fill='#00BFB2' />
                        }
                      </Flex>
                    </Box>
                  ))
                }
              </Box>
            </Box>
          }
        </Flex>
        <Button
          onClick={() => {
            router.push(
              route({ pathname: '/[lng]/company/[companyId]/equity-incentive-grants/management', query: { lng, companyId } })
            )
          }}
          variant='secondary'
          px='1.5em'
          py='0.5em'
          _hover={{ bg: 'secondary.100' }}>
          {t('captable_tabs.see_more_details')}
        </Button>
      </Flex>

      <Flex justify="space-between" mb={8} p={4} flexWrap='wrap' gap={{ base: '2rem', md: 0 }}>
        <Flex flex={1} flexDirection='column'>
          <StatCard
            image='/assets/total_stocks.svg'
            label="Total Stocks"
            value={(data as any).totalShareCount.formatted}
            color="blue.500"
          />
          {
            selectedProgram &&
            <Flex flex={1} align="start" gap={4} flexDirection='row' alignItems='center' mt={10}>
              <DonutChart data={[
                { name: 'Granted', value: selectedProgram.grantedShares.n.value, color: '#6366F1' },
                { name: 'Available', value: selectedProgram.availableShares.n.value, color: '#00BFB2' },
              ]} />
              <Legend data={[
                { name: 'Granted', value: selectedProgram.grantedShares.p.formatted, color: '#6366F1', count: selectedProgram.grantedShares.n.value },
                { name: 'Available', value: selectedProgram.availableShares.p.formatted, color: '#00BFB2', count: selectedProgram.availableShares.n.value },
              ]} />
            </Flex>
          }
        </Flex>
        <Flex flex={1} flexDirection='column'>
          <Flex justifyContent='space-between' flexWrap='wrap'>
            <StatCard
              image='/assets/contracts.svg'
              label="Contracts"
              value={(data as any).grantsCount.formatted}
              color="blue.500"
            />
            <StatCard
              image='/assets/grantees.svg'
              label="Grantees"
              value={(data as any).granteeCount.formatted}
              color="blue.500"
            />
          </Flex>
          {
            selectedProgram &&
            <Flex flex={1} align="start" gap={4} flexDirection='row' alignItems='center' mt={10}>
              <DonutChart data={[
                { name: 'Active', value: selectedProgram.activeGrants.n.value, color: '#00BFB2' },
                { name: 'Completed', value: selectedProgram.finishedGrants.n.value, color: '#F59E0B' },
              ]} />
              <Legend data={[
                { name: 'Active', value: selectedProgram.activeGrants.n.value, color: '#00BFB2' },
                { name: 'Completed', value: selectedProgram.finishedGrants.n.value, color: '#F59E0B' },
              ]} />
            </Flex>
          }
        </Flex>
      </Flex>
    </Box>
  );
};

function StatCard({ image, label, value }) {
  return <Flex gap={2} align="start">
    <Flex gap={3}>
      <Image src={image} alt={label} width='3rem' height='3rem' />
      <VStack align="start" gap={0}>
        <Box fontSize="sm" color="gray.500">
          {label}:
        </Box>
        <Box fontSize="2xl" fontWeight="bold">
          {value.toLocaleString()}
        </Box>
      </VStack>
    </Flex>
  </Flex>
};

function DonutChart({ data, size = 120 }) {
  const chartColors = data.map((item) => item.color)

  return <ResponsiveContainer width={size} height={size}>
    <PieChartContainer
      lng='ar'
      variant='thin'
      data={data}
      dataKey='value'
      colors={chartColors}
      innerRadius={50}
      outerRadius={60}
      showTotal={false}
    />
  </ResponsiveContainer>
};

function Legend({ data }) {
  console.log('Legend', data);
  return <VStack align="start" gap={2}>
    {data.map((item, index) => (
      (
        <Flex key={index} gap={2} alignItems='center'>
          <Box w={3} h={3} borderRadius="full" bg={item.color} />
          <Box fontSize="sm">
            <Flex gap={1} alignItems='center'>
              <Box as="span" fontWeight="bold">{item.name}</Box>
              <Box as="span" color={item.color} ml={1} fontWeight='bold'>
                {item.value}
              </Box>
              {item.count &&
                <Box color='gray.500' fontWeight='bold'>
                  ({item.count.toLocaleString()})
                </Box>
              }
            </Flex>
          </Box>
        </Flex>
      )
    ))}
  </VStack>
};

export default EquityIncentivesDashboard;