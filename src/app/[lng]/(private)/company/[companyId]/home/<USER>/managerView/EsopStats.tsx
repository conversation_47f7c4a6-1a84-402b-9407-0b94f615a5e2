import PieChartContainer from '@/components/EbPieChart'
import EmptyResult from '@/components/EmptyResult'
import MultiLineTransMessage from '@/components/MultiLineTransMessage'
import CardLoading from '@/components/loading/Card'
import { Tooltip } from '@/components/ui/tooltip'
import { graphql } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { formatAboveThousand } from '@/util/number'
import { Box, Flex, GridItem, Image, Stack } from '@chakra-ui/react'
import { PiWarningCircle } from 'react-icons/pi'
import { useQuery } from 'urql'

const Query = graphql(`
  query Query($id: ID!) {
    company(id: $id) {
      equityIncentiveReports {
        totalShareCount {
          formatted
        }
        availableShares {
          ...Number
        }
        vestingShares {
          ...Number
        }
        vestedShares {
          ...Number
        }
        exercisingShares {
          ...Number
        }
        settledShares {
          ...Number
        }
        finishedGrants {
          ...Number
        }
        activeGrants {
          ...Number
        }
        terminatedGrants {
          ...Number
        }
      }
    }
  }
  fragment Number on NumberToTotal {
    n {
      value
    }
  }
`)

export default function EsopStats({ locked, companyId }) {
  const { t, lng } = useEbanaLocale()

  const [{ data }] = useQuery({
    query: Query,
    variables: {
      id: companyId,
    },
  })

  let renderedContent

  if (!data) {
    renderedContent = <CardLoading numberOfCards={2} />
  } else {
    let totalStocksList,
      contractList = null

    if (data && data.company) {
      const {
        availableShares,
        vestingShares,
        vestedShares,
        exercisingShares,
        settledShares,
        activeGrants,
        terminatedGrants,
        finishedGrants,
      } = data.company.equityIncentiveReports

      totalStocksList = [
        { stocks: availableShares.n.value, type: t('available') },
        { stocks: vestingShares.n.value, type: t('vesting') },
        { stocks: vestedShares.n.value, type: t('vested') },
        { stocks: exercisingShares.n.value, type: t('exercising') },
        { stocks: settledShares.n.value, type: t('exercised') },
      ]
      contractList = [
        { numberOfContracts: activeGrants.n.value, type: t('active') },
        {
          numberOfContracts: terminatedGrants.n.value,
          type: t('terminated'),
        },
        {
          numberOfContracts: finishedGrants.n.value,
          type: t('completed'),
        },
      ]
    }

    let renderedTotalStocks
    let allContracts =
      data?.company?.equityIncentiveReports.finishedGrants.n.value +
      data?.company?.equityIncentiveReports.activeGrants.n.value +
      data?.company?.equityIncentiveReports.terminatedGrants.n.value

    if (!totalStocksList) {
      return null
    } else {
      renderedTotalStocks = (
        <Stack>
          <Flex alignItems='center'>
            <Box textStyle='body4' color='typography.200'>
              {t('total_esop_stocks')}:{' '}
              <Box as='span' textStyle='h5' fontWeight={700} color='typography.100'>
                {data?.company?.equityIncentiveReports.totalShareCount.formatted}
              </Box>
            </Box>
            <Tooltip
              content={<MultiLineTransMessage message={t('company_esop_stat_description')} />}
              contentProps={{
                color: '#fff',
                background: '#101828',
                fontSize: '0.75rem',
                fontWeight: '600',
                px: '0.75rem',
                py: '0.5rem',
                borderRadius: '8px',
              }}
              positioning={{ placement: 'top' }}>
              <Box ms='0.58rem'>
                <PiWarningCircle color='#4E5D78' />
              </Box>
            </Tooltip>
          </Flex>
          <PieChartContainer
            lng={lng}
            variant='thin'
            showTotal={false}
            colors={['#1C94ED', '#00BFB2', '#5858FF', '#FEC412']}
            data={totalStocksList}
            showLegend={true}
            dataKey='stocks'
            legendKeyAr='type'
            legendKeyEn='type'
          />
        </Stack>
      )
    }

    let renderedTotalContracts
    if (!contractList) {
      renderedTotalContracts = <EmptyResult title={t('no-stocks-found')} description='' />
    } else {
      renderedTotalContracts = (
        <Stack>
          <Box textStyle='body4' color='typography.200'>
            {t('esop_contract_number')}:{' '}
            <Box as='span' textStyle='h5' fontWeight={700} color='typography.100'>
              {formatAboveThousand(allContracts)}
            </Box>
          </Box>
          <PieChartContainer
            lng={lng}
            variant='thin'
            showTotal={false}
            colors={['#6A6AF1', '#00BFB2', '#FEC412', '#FF5630']}
            data={contractList}
            showLegend={true}
            dataKey='numberOfContracts'
            legendKeyAr='type'
            legendKeyEn='type'
          />
        </Stack>
      )
    }

    renderedContent = (
      <Flex direction={{ base: 'column', xl: 'row' }} gap='3.94em'>
        {renderedTotalStocks}
        {renderedTotalContracts}
      </Flex>
    )
  }

  return (
    <GridItem colSpan={2}>
      <Box
        background='background.400'
        py='1.5625em'
        px='1.875em'
        borderRadius='14px'
        minHeight='100%'
        position='relative'>
        {locked ? <Image position='absolute' top='50%' left='50%' src='/assets/locked.svg' alt='locked' /> : null}

        <div
          style={{
            filter: locked ? 'blur(8px)' : 'blur(0)',
          }}
          id='esop-summary'>
          <Box textStyle='h4' fontWeight={700} color='#121423'>
            {t('esop_summary')}
          </Box>
          <Box height='1px' backgroundColor='gray.200' mt='1em' mb='1.875em' width='100%' />

          {renderedContent}
        </div>
      </Box>
    </GridItem>
  )
}
