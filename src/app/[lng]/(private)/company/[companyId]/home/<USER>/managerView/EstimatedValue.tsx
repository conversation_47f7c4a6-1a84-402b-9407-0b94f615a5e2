import { RiyalNewSignIcon } from '@/components/EbanaIcons'
import StockChange from '@/components/StockChange'
import { Tooltip } from '@/components/ui/tooltip'
import MarketValueChart from '@/features/register/MarketValueChart'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Flex, GridItem, Image, Stack } from '@chakra-ui/react'
import { PiWarningCircle } from 'react-icons/pi'

EstimatedValue.registerFragment = graphql(
  `
    fragment EstimatedValue on ShareholderRegister {
      estimatedMarketCap {
        formatted
      }
      ticker {
        change {
          complement
          complementFormatted
          formatted
          value
        }
        weekly {
          __typename
        }
        last {
          formatted
          value
        }
      }
      ...MarketValueChart
    }
  `,
  [MarketValueChart.registerFragment]
)

EstimatedValue.equityFragment = graphql(
  `
    fragment EquityEstimatedValue on EquityPositionRegister {
      estimatedMarketCap {
        formatted
      }
      ticker {
        change {
          complement
          complementFormatted
          formatted
          value
        }
        weekly {
          __typename
        }
        last {
          formatted
          value
        }
      }
      ...EquityMarketValueChart
    }
  `,
  [MarketValueChart.equityFragment]
)
export default function EstimatedValue({
  registerData,
  equityData,
  locked = false,
  isTitle = true,
}: {
  registerData?: FragmentOf<typeof EstimatedValue.registerFragment>
  equityData?: FragmentOf<typeof EstimatedValue.equityFragment>
  locked?: boolean
  isTitle?: boolean
}) {
  const { t } = useEbanaLocale()

  if (registerData == null && equityData == null) return

  const registerResult = readFragment(EstimatedValue.registerFragment, registerData)
  const equityResult = readFragment(EstimatedValue.equityFragment, equityData)

  const { estimatedMarketCap, ticker } = registerResult || equityResult

  let renderedContent

  if (ticker == null || ticker.weekly.length === 0) {
    return
  }

  renderedContent = (
    <Stack gap='2.25em'> 
      <MarketValueChart equityData={equityResult} registerData={registerResult} withXAxis={false} withYAxis={false} withCartesianGrid={false} chartHeight={300} />
    </Stack>
  )

  return (
    <GridItem colSpan={2}>
      {renderedContent}
    </GridItem>
  )
}
