import { Box, Flex, Image } from '@chakra-ui/react'

export default function LinkHeaderCard(props) {
  const { id, title, alt, src, onClick } = props

  return (
    <Box
      id={id}
      className='shortcut'
      onClick={onClick}
      background='background.400'
      py='0.85em'
      px='1.1em'
      borderRadius='14px'
      cursor='pointer'
      _hover={{
        textDecoration: 'none',
      }}>
      <Flex alignItems='center' gap='0.75em'>
        <Box position='relative'>
          <Box
            zIndex={1}
            position='absolute'
            alignSelf='end'
            marginTop='0.8em'
            borderWidth='2px'
            borderColor='#fff'
            backgroundColor='primary.200'
            borderRadius='50%'
            padding='0.1em'>
            <Image height='0.8em' src='/assets/add.svg' alt='' />
          </Box>
          <Box>
            <Image src={src} alt={alt} height='2em' width='2em' />
          </Box>
        </Box>
        <Box>
          <Box textStyle='btnLg' fontWeight={700} color='primary.200'>
            {title}
          </Box>
        </Box>
      </Flex>
    </Box>
  )
}
