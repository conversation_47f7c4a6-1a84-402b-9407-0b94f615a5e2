import { useTranslation } from '@/app/i18n/client'
import { ApplicationPropsContext } from '@/context/application-props'
import { graphql } from '@/graphql'
import React from 'react'
import PieChartWithList from '../PieChartWithList'

ShareholdersChart.fragment = graphql(`
  fragment ShareholdersChart on ShareholderRegister {
    totalShareholders {
      formatted
    }
    top5 {
      shareholders {
        name
        totalOwnership {
          value
          formatted
        }
      }
      ownership {
        value
        formatted
        complement
        complementFormatted
      }
    }
  }
`)

export default function ShareholdersChart({ data }: { data: any }) {
  const { lng } = React.useContext(ApplicationPropsContext)
  const { t } = useTranslation(lng, 'Pages')

  const pieData = [
    ...data.top5.shareholders.map((shareholder: any) => ({
      label: shareholder.name,
      value: shareholder.totalOwnership.value,
      displayedValue: shareholder.totalOwnership.formatted,
    })),
    {
      label: t('others'),
      value: data.top5.ownership.complement,
      displayedValue: data.top5.ownership.complementFormatted,
    },
  ]

  return (
    <PieChartWithList
      data={pieData}
      colors={['#003654', '#00BFB2', '#1C94ED', '#FEC412', '#FF5630']}
      width={240}
      height={240}
      showAvatar
      showTotal
      customTitle={t('total_shareholders')}
      customValue={data.totalShareholders.formatted}
    />
  )
}
