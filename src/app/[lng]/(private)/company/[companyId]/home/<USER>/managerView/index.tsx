import { useTranslation } from '@/app/i18n/client'
import { ErrorBoundary } from '@/components/ErrorBoundary'
import Header from '@/components/header'
import NavigationContainer from '@/components/Navigation'
import { UnitErrorBox } from '@/components/UnitErrorBox'
import { ApplicationPropsContext } from '@/context/application-props'
import { CompanyContext } from '@/context/company'
import { useAuth } from '@/context/new-auth'
import { graphql } from '@/graphql'
import { Box, Flex, Stack } from '@chakra-ui/react'
import React from 'react'
import { useQuery } from 'urql'
import CaptableCharts from './CaptableCharts'
import ShareclassesChart from './charts/ShareclassesChart'
import ShareholdersChart from './charts/ShareholdersChart'
import EmptyStateCards from './EmptyStateCards'
import EquityIncentivesDashboard from './EquityIncentives'
import EstimatedValue from './EstimatedValue'
import { KeyServices } from './KeyServices'
import { OnboardingProgress } from './OnboardingProgress'

const HomePageQuery = graphql(
  `
    query HomePageQuery($id: ID!) {
      company(id: $id) {
        equityIncentiveReports {
          ...EquityIncentivesDashboard
        }
        register {
          ...ShareclassesChart
          ...ShareholdersChart
          ...EstimatedValue
        }
        onboarding {
          ...OnboardingProgress
        }
      }
    }
  `,
  [
    EstimatedValue.registerFragment,
    OnboardingProgress.fragment,
    ShareholdersChart.fragment,
    ShareclassesChart.fragment,
    EquityIncentivesDashboard.fragment,
  ]
)

export function ManagerView() {
  const { lng } = React.useContext(ApplicationPropsContext)
  const { id } = React.useContext(CompanyContext)
  const { t } = useTranslation(lng, 'Pages')
  const { name } = useAuth()

  const [{ data }] = useQuery({ query: HomePageQuery, variables: { id } })

  if (!data) return

  return (
    <NavigationContainer
      pageVariant='normal'
      header={
        <Header
          title={
            <Stack gap={0}>
              <Box lineHeight='0.75' textStyle='body400' fontWeight={600}>
                {t('welcome_user', {
                  name: name.first,
                })}
              </Box>
              <Box textStyle='h1' fontWeight={700}>
                {t('welcome')}
              </Box>
            </Stack>
          }
        />
      }>
      <Flex
        justifyContent='space-between'
        flexWrap='wrap'
        gap={{ base: '1rem', md: '1.875rem' }}
        mx={{ base: '1rem', md: '1.875rem' }}
        mt={{ base: '1rem', md: '0px' }}>
        <Stack minWidth={0}>
          <ErrorBoundary fallback={<UnitErrorBox title={t('empty_state')} />}>
            <EmptyStateCards data={data.company.onboarding} />
          </ErrorBoundary>
          <ErrorBoundary fallback={<UnitErrorBox title={t('empty_state')} />}>
            <EstimatedValue registerData={data.company.register} />
          </ErrorBoundary>
          <EquityIncentivesDashboard data={data.company.equityIncentiveReports} />
          <ErrorBoundary fallback={<UnitErrorBox title={t('cap_table_summary')} />}>
            <CaptableCharts data={data.company.register} />
          </ErrorBoundary>
          <Box>
            <KeyServices />
          </Box>
        </Stack>
        <Stack
          mb='1.4375em'
          flex='1 1 0'
          flexWrap='wrap'
          maxWidth={{ base: '100%', md: '21.875rem' }}
          gap={{ base: '1rem', md: '1.875rem' }}
          width={{ base: '100%', md: '21.875rem' }}>
          <Box w='100%' height='30rem' bg='white'></Box>
          <ErrorBoundary fallback={<UnitErrorBox title={t('empty_state')} />}>
            <Box p='1rem'>
              <OnboardingProgress data={data.company.onboarding} />
            </Box>
          </ErrorBoundary>
        </Stack>
      </Flex>
    </NavigationContainer>
  )
}
