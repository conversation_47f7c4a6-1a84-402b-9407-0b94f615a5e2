'use client'

import { Chart, useChart } from '@chakra-ui/charts'
import { Area, AreaChart, Tooltip, XAxis, YAxis } from 'recharts'

export function AreaChartComponent({ data, series, xAxisKey }: { data: any; series: any; xAxisKey: string }) {
  const chart = useChart({
    data: data,
    series: series,
  })

  return (
    <Chart.Root maxH='sm' chart={chart}>
      <AreaChart accessibilityLayer data={chart.data} margin={{ bottom: 24, left: 24 }}>
        <XAxis
          dataKey={chart.key(xAxisKey)}
          tickMargin={8}
          tickFormatter={(value) => value.slice(0, 3)}
          stroke={chart.color('border')}
        />
        <YAxis stroke={chart.color('border')} />
        <Tooltip cursor={false} animationDuration={100} content={<Chart.Tooltip />} />
        {chart.series.map((item) => (
          <Area
            type='natural'
            key={JSON.stringify(item.name)}
            isAnimationActive={false}
            dataKey={chart.key(item.name) as any}
            fill={chart.color(item.color)}
            fillOpacity={0.2}
            stroke={chart.color(item.color)}
            stackId='a'
          />
        ))}
      </AreaChart>
    </Chart.Root>
  )
}
