import Avatar, { AvatarGroup } from '@/components/Avatar'
import Persona from '@/components/Persona'
import { Box, Stack } from '@chakra-ui/react'

export default function Avatars() {
  const list = ['amer alghfaili', 'o<PERSON> alora<PERSON>', 'isam maher', 'faisal alora<PERSON>', 'asem alfuhaid']

  return (
    <Stack>
      <Box textStyle='h4'>Normal</Box>
      <Avatar name='Amer Alghfaili' />
      <Box textStyle='h4'>With Default Badge</Box>
      <Avatar name='Amer Alghfaili' badge={true} />
      <Box textStyle='h4'>With Custom Badge</Box>
      <Avatar name='Amer Alghfaili' badge='/assets/visa.png' />
      <Box textStyle='h4'>Ebana Avatar</Box>
      <Avatar name='Amer Alghfaili' isEbana={true} />
      <Box textStyle='h4'>Persona</Box>
      <Persona name='<PERSON>' addon='<EMAIL>' Avatar={<Avatar name='<PERSON>' />} />
      <Box textStyle='h4'>Avatar group</Box>
      <AvatarGroup
        avatars={list.map((name) => {
          return <Avatar key={name} name={name} />
        })}
        max={3}
      />
      <Box textStyle='h4'>Avatar group (custom overflow indicator)</Box>
      <AvatarGroup
        avatars={list.map((name) => {
          return <Avatar key={name} name={name} />
        })}
        max={3}
        OverflowIndicator={(remaining) => (
          <Avatar name={remaining.toString()} isEbana={true} width='5em' height='5em' />
        )}
      />
    </Stack>
  )
}
