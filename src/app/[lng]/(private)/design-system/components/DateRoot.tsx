import ClockFormatRoot from '@/components/datetime/ClockFormatRoot'
import DateRoot from '@/components/datetime/DateRoot'
import TimeRoot from '@/components/datetime/TimeRoot'
import { Stack } from '@chakra-ui/react'

function DateRootUsage() {
  return (
    <Stack position='relative' width='60%' py={4} mx='auto' alignContent='center' display='flex'>
      <DateRoot
        dateLabel='Date'
        dateProps={{ day: 1, month: 1, year: 2025 }}
        hourValue={12}
        minuteValue={30}
        clockFormat='pm'
        onChangeIso={(i) => {
          console.log(i, 'ffffff')
        }}
        display='flex'
        gap='1em'
        alignItems='center'
        w='80%'>
        <TimeRoot display='flex' gap='1em' />
        <ClockFormatRoot display='flex' gap='1em' flexDirection='row' />
      </DateRoot>
    </Stack>
  )
}

export default DateRootUsage
