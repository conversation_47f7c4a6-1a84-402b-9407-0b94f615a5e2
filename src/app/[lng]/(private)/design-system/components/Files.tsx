import { UpdateIcon } from '@/components/EbanaIcons'
import { File } from '@/components/file'
import { Box, IconButton, Stack } from '@chakra-ui/react'

export function Files() {
  return (
    <Stack>
      <Box textStyle='h4'>PDF file</Box>
      <File name='File Name' url='' size={10} type='application/pdf' />
      <Box textStyle='h4'>Image</Box>
      <File name='File Name' url='' size={10} type='image/png' />
      <Box textStyle='h4'>File with actions</Box>
      <File name='File Name' url='' size={10} type='image/png'>
        <IconButton variant='plain'>
          <UpdateIcon w='1.2rem' h='1.2rem' />
        </IconButton>
      </File>
    </Stack>
  )
}
