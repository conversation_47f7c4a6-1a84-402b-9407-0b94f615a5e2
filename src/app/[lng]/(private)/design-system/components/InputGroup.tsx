import { Input, InputGroup, NativeSelect } from '@chakra-ui/react'
import { LuUser } from 'react-icons/lu'

const DomainSelect = () => (
  <NativeSelect.Root size='xs' variant='plain' width='auto' me='-1'>
    <NativeSelect.Field defaultValue='.com' fontSize='sm'>
      <option value='.com'>.com</option>
      <option value='.org'>.org</option>
      <option value='.net'>.net</option>
    </NativeSelect.Field>
    <NativeSelect.Indicator />
  </NativeSelect.Root>
)

export function CustomInputGroup() {
  return (
    <>
      <InputGroup endAddon='.com'>
        <Input placeholder='yoursite' />
      </InputGroup>

      <InputGroup mt={2} startElement={<LuUser />}>
        <Input placeholder='Username' />
      </InputGroup>

      <InputGroup flex='1' mt={2} startElement='https://' endElement={<DomainSelect />}>
        <Input ps='4.75em' pe='0' />
      </InputGroup>
    </>
  )
}
