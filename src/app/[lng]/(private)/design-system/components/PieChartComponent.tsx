"use client"

import { Chart, use<PERSON><PERSON> } from "@chakra-ui/charts"
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Toolt<PERSON> } from "recharts"

export function PieChartComponent ({data}: { data: any }) {
  const chart = useChart({data})

  return (
    <Chart.Root boxSize="200px" chart={chart} mx="auto">
      <PieChart>
        <Tooltip
          cursor={false}
          animationDuration={100}
          content={<Chart.Tooltip hideLabel />}
        />
        <Pie
          innerRadius={80}
          outerRadius={100}
          isAnimationActive={false}
          data={chart.data}
          dataKey={chart.key("value")}
          nameKey="name"
        >
          {chart.data.map((item) => {
            return <Cell key={item.name} fill={chart.color(item.color)} />
          })}
        </Pie>
      </PieChart>
    </Chart.Root>
  )
}
