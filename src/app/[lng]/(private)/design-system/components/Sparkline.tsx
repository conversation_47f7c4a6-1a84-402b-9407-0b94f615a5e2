'use client'

import { Chart, useChart, UseChartProps } from '@chakra-ui/charts'
import { Area, AreaChart } from 'recharts'

type SparklineProps = {
  data: { date: string; value: number }[]
  series: UseChartProps<any>['series']
}

export const Sparkline = ({ data, series }: SparklineProps) => {
  const chart = useChart({
    data,
    series,
  })

  return (
    <Chart.Root width='28' height='12' chart={chart}>
      <AreaChart data={chart.data}>
        <defs>
          <Chart.Gradient
            id='sp-gradient'
            stops={[
              { offset: 0, color: 'green.solid', opacity: 0.8 },
              { offset: 1, color: 'green.solid', opacity: 0.2 },
            ]}
          />
        </defs>
        {chart.series.map((item) => (
          <Area
            key={item.name}
            isAnimationActive={false}
            dataKey={chart.key(item.name)}
            fill='url(#sp-gradient)'
            fillOpacity={0.2}
            stroke={chart.color(item.color)}
            strokeWidth={2}
          />
        ))}
      </AreaChart>
    </Chart.Root>
  )
}
