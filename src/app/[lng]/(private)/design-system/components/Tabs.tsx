import { Tabs as ChakraTabs } from '@chakra-ui/react'
import React from 'react'

export function Tabs() {
  const [value, setValue] = React.useState('1')

  return (
    <ChakraTabs.Root value={value} onValueChange={({ value }) => setValue(value)}>
      <ChakraTabs.List>
        <ChakraTabs.Trigger value='1'>One</ChakraTabs.Trigger>
        <ChakraTabs.Trigger value='2'>Two</ChakraTabs.Trigger>
      </ChakraTabs.List>
      <ChakraTabs.Content value='1'>One Content</ChakraTabs.Content>
      <ChakraTabs.Content value='2'>Two Content</ChakraTabs.Content>
    </ChakraTabs.Root>
  )
}
