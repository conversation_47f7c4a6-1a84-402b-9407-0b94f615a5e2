import Avatar, { AvatarGroup } from '@/components/Avatar'
import { ClockIcon } from '@/components/EbanaIcons'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Button, Flex, Image } from '@chakra-ui/react'
import NextLink from 'next/link'
import { route } from 'nextjs-routes'
import { FaArrowLeft, FaArrowRight } from 'react-icons/fa'

OnGoingMeeting.fragment = graphql(`
  fragment OnGoingMeeting on GovernanceMeeting {
    id
    groupType
    groupId
    members {
      name
    }
    title
    date {
      time {
        default
      }
      date {
        default
      }
      month {
        label
      }
      year
      dayOfMonth
    }

    company {
      tradeName
      logo {
        url
      }
    }
  }
`)

export function OnGoingMeeting({ data }: { data: FragmentOf<typeof OnGoingMeeting.fragment> }) {
  const { t , lng} = useEbanaLocale()

  const onGoingMeeting = readFragment(OnGoingMeeting.fragment, data)

  return (
    <Box p='1.875rem' pt={0}>
      <Flex
        bg='gray.800'
        p={4}
        borderRadius='xl'
        align='center'
        justify='space-between'
        boxShadow='lg'
        color='white'
        flexWrap='wrap'
        gap={5}>
        <Flex
          color='#ff5631'
          direction='column'
          borderRadius='md'
          overflow='hidden'
          align='center'
          minW='4rem'
          bg='white'>
          <Box
            textStyle='body3'
            color='white'
            bg='#ff5631'
            width='full'
            textAlign='center'
            borderBlockColor='#ff5631'
            borderBottomWidth='0.2rem'>
            {onGoingMeeting.date.month.label}
          </Box>
          <Box textStyle='body4' fontWeight='bold'>
            {onGoingMeeting.date.dayOfMonth}
          </Box>
          <Box textStyle='body4' fontWeight='bold'>
            {onGoingMeeting.date.year}
          </Box>
        </Flex>

        <Box flex='1'>
          <Box fontWeight='bold'>{onGoingMeeting.title}</Box>
          <Flex gap={2} mt={1} fontSize='sm' color='gray.300' alignItems='center' direction='row'>
            <ClockIcon />
            <Box>{onGoingMeeting.date.time.default}</Box>
            <Box width={1} height={1} backgroundColor='white' borderRadius='full' />
            {onGoingMeeting.company.logo && (
              <Image src={onGoingMeeting.company.logo.url} width={4} height={4} borderRadius='full' alt='' />
            )}
            <Box maxWidth='10rem' lineClamp={1}>
              {onGoingMeeting.company.tradeName}
            </Box>
          </Flex>
        </Box>

        <AvatarGroup
          avatars={onGoingMeeting.members.map((member) => {
            return <Avatar key={member.name} name={member.name} />
          })}
          max={3}
          OverflowIndicator={(remaining) => <Avatar name={remaining.toString()} isEbana={false} />}
        />

        <Flex alignItems='center' gap={10}>
          <Box
            backgroundColor='#2e2221'
            borderRadius='full'
            px='0.8rem'
            py='0.5rem'
            display='flex'
            alignItems='center'
            gap='0.5rem'>
            <Box width={4} height={4} borderRadius='full' backgroundColor='#ff5631' />
            <Box color='#ff5631' fontWeight='bold'>
              {t('ongoing')}
            </Box>
          </Box>
          <NextLink
            href={
              onGoingMeeting.groupType === 'BOARD_OF_DIRECTORS'
                ? route({
                    pathname: '/[lng]/individual/board/[boardId]/meetings/[id]',
                    query: { lng, boardId: onGoingMeeting.groupId, id: onGoingMeeting.id },
                  })
                : route({
                    pathname: '/[lng]/individual/committees/[committeeId]/meetings/[id]',
                    query: { lng, committeeId: onGoingMeeting.groupId, id: onGoingMeeting.id },
                  })
            }>
            <Button
              variant='primary'
              size='sm'
              display='flex'
              alignItems='center'
              gap='1rem'
              px='3rem'
              py='1rem'
              borderRadius='14px'>
              <Box textStyle='body4'>{t('join_now')}</Box>

              {lng === 'ar' ? (
                <FaArrowLeft fill='white' stroke='white' />
              ) : (
                <FaArrowRight fill='white' stroke='white' />
              )}
            </Button>
          </NextLink>
        </Flex>
      </Flex>
    </Box>
  )
}
