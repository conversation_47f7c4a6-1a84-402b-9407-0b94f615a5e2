import { Box, Flex, FlexProps, IconProps } from '@chakra-ui/react'
import React from 'react'

export function NewStatus({ label, icon, ...rest }: { label: React.ReactNode; icon: React.ReactElement } & FlexProps) {
  return (
    <Flex gap='0.3125em' py='0.375em' px='0.625em' w='fit-content' borderRadius='20px' {...rest}>
      {React.cloneElement(icon as React.ReactElement<IconProps>, {
        width: '0.875rem',
        height: '0.875rem',
      })}
      <Box textStyle='body5' color='typography.100'>
        {label}
      </Box>
    </Flex>
  )
}
