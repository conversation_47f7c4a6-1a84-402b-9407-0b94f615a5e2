import { Avatar, Box, Flex, FlexProps, Stack } from '@chakra-ui/react'
import React from 'react'

export default function Persona({
  name,
  addon,
  Avatar,
  ...rest
}: {
  name: React.ReactNode
  addon?: React.ReactNode
  Avatar: React.ReactElement<Avatar.RootProps>
} & FlexProps) {
  return (
    <Flex gap='0.75em' alignItems='center' {...rest}>
      {Avatar}
      <Stack gap={0}>
        {typeof name !== 'object' ? (
          <Box
            lineClamp={1}
            textAlign='start'
            overflow='clip'
            textStyle='body3'
            fontWeight={500}
            color='typography.100'>
            {name}
          </Box>
        ) : (
          name
        )}
        {addon != null &&
          (typeof addon !== 'object' ? (
            <Box textStyle='body4' color='typography.200'>
              {addon}
            </Box>
          ) : (
            addon
          ))}
      </Stack>
    </Flex>
  )
}
