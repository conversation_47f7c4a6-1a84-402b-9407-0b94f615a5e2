import { RadioCardItem, RadioCardRoot } from '@/components/ui/radio-card'
import { Box, Flex, Image } from '@chakra-ui/react'
import { useTranslation } from 'react-i18next'
import { useDateTimeContext } from './DateTimeContext'

 function ClockFormatRoot({...rest}) {
  const { t } = useTranslation('Pages')
  const { time, setClockFormat } = useDateTimeContext()  

  const clockFormat = time.clockFormat

  return (
    <Box >
      <RadioCardRoot
        value={clockFormat}
        onValueChange={({ value }) => {
          setClockFormat(value as 'am' | 'pm')
        }}
        {...rest}
      >
        <RadioCardItem
          value='am'
          borderRadius='12px'
          alignSelf='flex-end'
          background='background.300'
          borderWidth='1px'
          borderStyle='solid'
          borderColor='primary.200'
          pt='0.2em'
          pb='0.1em'
          px='0.85em'
          _checked={{
            bg: 'primary.200',
            borderWidth: 0,
          }}
          label={
            <Flex direction='column' alignItems='center' justifyContent='center' py='0.2em'>
              <Image
                width='1.125em'
                height='1.125em'
                src={clockFormat === 'am' ? '/assets/sun-white.svg' : '/assets/sun.svg'}
                alt=''
              />
              <Box
                textStyle='inputSm'
                color={clockFormat === 'am' ? '#fff' : 'primary.200'}
                fontWeight={500}
              >
                {t('am_clock_format')}
              </Box>
            </Flex>
          }
        />
        <RadioCardItem
          value='pm'
          borderRadius='12px'
          alignSelf='flex-end'
          background='background.300'
          borderWidth='1px'
          borderStyle='solid'
          borderColor='primary.200'
          pt='0.2em'
          pb='0.1em'
          px='0.85em'
          _checked={{
            bg: 'primary.200',
            borderWidth: 0,
          }}
          label={
            <Flex direction='column' alignItems='center' justifyContent='center' py='0.2em'>
              <Image
                width='1.125em'
                height='1.125em'
                src={clockFormat === 'pm' ? '/assets/moon-white.svg' : '/assets/moon.svg'}
                alt=''
              />
              <Box
                textStyle='inputSm'
                color={clockFormat === 'pm' ? '#fff' : 'primary.200'}
                fontWeight={500}
              >
                {t('pm_clock_format')}
              </Box>
            </Flex>
          }
        />
      </RadioCardRoot>
    </Box>
  )
}

export default ClockFormatRoot
