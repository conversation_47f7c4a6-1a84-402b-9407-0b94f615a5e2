'use client'

import { Box } from '@chakra-ui/react'
import React from 'react'
import DatePicker from './DatePicker'
import {
  DateState,
  DateTimeProvider,
  TimeState,
  useDateTimeContext,
} from './DateTimeContext'

type DateRootProps = {
  dateLabel: string
  dateProps: DateState
  hourValue: number
  minuteValue: number
  clockFormat: 'am' | 'pm'
  children?: React.ReactNode
  onChangeIso?: (value: { date: DateState; time: TimeState, clockFormat: 'am' | 'pm' }) => void
} & React.ComponentProps<typeof Box>

 

// Hooked version of DatePicker using context
function DatePickerControlled({ label }: { label: string }) {
  const { date, setDate } = useDateTimeContext()

  return (
    <DatePicker
      label={label}
      day={date.day}
      month={date.month}
      year={date.year}
      onChangeToIso={(iso) => {
        const [y, m, d] = iso.split('-').map(Number)
        setDate({ day: d, month: m, year: y })
      }}
    />
  )
}

//  Main Component
function DateRoot({
  dateLabel,
  dateProps,
  hourValue,
  minuteValue,
  clockFormat,
  children,
  onChangeIso,
  ...rest
}: DateRootProps) {
  return (
    <DateTimeProvider
      initialDate={dateProps}
      initialTime={{ hour: hourValue, minute: minuteValue, clockFormat }}
      onChangeIso={onChangeIso}
    >
      <Box {...rest}>
        <DatePickerControlled label={dateLabel} />
        {children}
      </Box>
    </DateTimeProvider>
  )
}

export default DateRoot
