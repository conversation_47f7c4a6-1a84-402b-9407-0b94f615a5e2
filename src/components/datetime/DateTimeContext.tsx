import React, { createContext, useContext, useEffect, useState } from 'react'

type ClockFormat = 'am' | 'pm'

export type DateState = {
  day: number
  month: number
  year: number
}

export type TimeState = {
  hour: number
  minute: number
  clockFormat: ClockFormat
}

type DateTimeContextType = {
  date: DateState
  time: TimeState
  setDate: (date: DateState) => void
  setHour: (hour: number) => void
  setMinute: (minute: number) => void
  setClockFormat: (format: ClockFormat) => void
}

const DateTimeContext = createContext<DateTimeContextType | null>(null)

export const useDateTimeContext = () => {
  const ctx = useContext(DateTimeContext)
  if (!ctx) throw new Error('useDateTimeContext must be used inside <DateRoot>')
  return ctx
}

type Props = {
  children: React.ReactNode
  initialDate: DateState
  initialTime: TimeState
  onChangeIso?: (value: { date: DateState; time: TimeState, clockFormat: 'am' | 'pm' }) => void
}

export const DateTimeProvider = ({ children, initialDate, initialTime, onChangeIso }: Props) => {
  const [date, setDate] = useState(initialDate)
  const [time, setTime] = useState(initialTime)

  // Convert to full ISO string when any value changes
  useEffect(() => {
    let hour = time.hour
    if (time.clockFormat === 'pm' && hour < 12) hour += 12
    if (time.clockFormat === 'am' && hour === 12) hour = 0

    const isoString = new Date(
      `${date.year}-${String(date.month).padStart(2, '0')}-${String(date.day).padStart(2, '0')}T${String(hour).padStart(2, '0')}:${String(time.minute).padStart(2, '0')}:00`
    ).toISOString()

    onChangeIso?.({ date, time, clockFormat: time.clockFormat })
  }, [date, time, onChangeIso])

  return (
    <DateTimeContext.Provider
      value={{
        date,
        time,
        setDate,
        setHour: (h) => setTime((prev) => ({ ...prev, hour: h })),
        setMinute: (m) => setTime((prev) => ({ ...prev, minute: m })),
        setClockFormat: (format) => setTime((prev) => ({ ...prev, clockFormat: format })),
      }}
    >
      {children}
    </DateTimeContext.Provider>
  )
}
