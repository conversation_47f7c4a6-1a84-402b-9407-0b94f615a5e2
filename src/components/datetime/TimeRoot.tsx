import { Box, BoxProps } from '@chakra-ui/react'
import { useTranslation } from 'react-i18next'
import { useDateTimeContext } from './DateTimeContext'
import { TimeField } from './Time'

function TimeRoot(props: BoxProps) {
  const { t } = useTranslation('Pages')
  const { time, setHour, setMinute } = useDateTimeContext()

  return (
    <Box {...props}>
      {/* Hours Field */}
      <TimeField
        label={t('hours')}
        placeholder={t('time_hour_placeholder')}
        value={time.hour}
        onChange={(value) => {
          setHour(value)
        }}
        position='relative'
        _after={{
          content: "':'",
          position: 'absolute',
          top: '45%',
          insetStart: '110%',
        }}
      />

      {/* Minutes Field */}
      <TimeField
        label={t('minutes')}
        placeholder={t('time_minute_placeholder')}
        value={time.minute}
        onChange={(value) => {
          if (!isNaN(value) && value >= 0 && value <= 60) {
            setMinute(value)
          }
        }}
      />
    </Box>
  )
}

export default TimeRoot
