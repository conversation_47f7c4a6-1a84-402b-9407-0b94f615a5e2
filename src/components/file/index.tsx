import { FormFragment } from '@/graphql/fragments'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import {
  Box,
  Flex,
  FlexProps,
  IconButton,
  Image,
  Link,
  LinkBox,
  LinkOverlay,
  SkeletonCircle,
  SkeletonText,
} from '@chakra-ui/react'
import { ResultOf } from 'gql.tada'
import React, { ReactNode } from 'react'
import { UseMutationExecute } from 'urql'
import { DeleteIcon, DocFileIcon, ExcelFileIcon, ImageFileIcon, PdfFileIcon, VideoFileIcon } from '../EbanaIcons'
import { ErrorBoundary } from '../ErrorBoundary'
import { EbanaSimpleForm } from '../form/EbanaSimpleForm'
import { UnitErrorBox } from '../UnitErrorBox'

const Truncate = (str: string, maxLength: number) => (str?.length > maxLength ? `${str.slice(0, maxLength)}...` : str)

type Props = {
  /**
   * Used to render the file name
   * This component wont display the file format, it's the component user's responsibility
   * to send it with the file name
   * The component will truncate the file name automatically using {@link Truncate}
   */
  name: string
  /**
   * Used to define type
   */
  type?: string
  /**
   * Used to render the download button (as <Link> element)
   */
  url?: string
  /**
   * Used to render the skeleton shape of this component
   */
  isLoading?: boolean
  /**
   * Used to render the file size, the user of this component
   * is responsible of defining what is the size format to be used
   */
  size?: number | string
  /**
   * Used to render delete button (styled), with EbanaSimpleForm
   * pass {@link File.DeleteForm} component to this prop
   */
  inlineUrl?: string // for review the file in the browser
  attachmentUrl?: string // for download the file
  DeleteForm?: React.ReactNode
  children?: ReactNode
} & FlexProps

export function File({
  name,
  url,
  isLoading = false,
  size,
  type = null,
  DeleteForm,
  children,
  inlineUrl,
  attachmentUrl,
  ...rest
}: Props) {
  const { t } = useEbanaLocale()

  if (isLoading) {
    return (
      <Flex
        alignItems='center'
        border='1px solid #F2F2F4'
        borderRadius='12px'
        ps='1em'
        pe='1.125em'
        pt='1em'
        pb='0.875em'
        {...rest}>
        <SkeletonCircle size='10' />
        <SkeletonText lineClamp={2} height='4' />
      </Flex>
    )
  }

  const previewLink = inlineUrl || url
  const downloadLink = attachmentUrl || url

  function SelectedIcon({ type }: { type: string }) {
    switch (type) {
      case 'image/jpeg':
        return <ImageFileIcon/>
      case 'image/png':
        return <ImageFileIcon/>
      case 'image/gif':
        return <ImageFileIcon/>
      case 'application/pdf':
        return <PdfFileIcon/>
      case 'application/vnd.ms-excel':
        return <ExcelFileIcon/>
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return <ExcelFileIcon/>
      case 'video/mp4':
        return <VideoFileIcon/>
      default:
        return <DocFileIcon/>
    }
  }

  return (
    <LinkBox>
      <Flex
        bg='#FAFCFE'
        alignItems='center'
        justifyContent='space-between'
        border='1px solid #F2F2F4'
        borderRadius='12px'
        ps='1em'
        pe='1.125em'
        pt='1em'
        pb='0.875em'
        cursor='pointer'
        {...rest}>
        <Flex gap='0.75rem'>
          <SelectedIcon type={type} />
          <Flex direction='column' gap='0.3em' me='1em' overflow='auto'>
            <Box lineClamp={1} textStyle='body4' fontWeight={500} color='#323B41' wordBreak='break-all'>
              {previewLink ? (
                <LinkOverlay href={previewLink} target='_blank'>
                  {Truncate(name, 50)}
                </LinkOverlay>
              ) : (
                Truncate(name, 50)
              )}
            </Box>
            {size != null && (
              <Box dir='ltr' textStyle='body5' color='typography.300'>
                {size}
              </Box>
            )}
          </Flex>
        </Flex>

        <Flex height='fit-content' mb='auto' gap='0.5em' zIndex={1}>
          {downloadLink && <DownloadFile url={downloadLink} name={name} />}
          <ErrorBoundary fallback={<UnitErrorBox title={t('delete')} />}>{DeleteForm}</ErrorBoundary>
          {children}
        </Flex>
      </Flex>
    </LinkBox>
  )
}

File.DeleteForm = function DeleteForm({
  form,
  mutation,
}: {
  form: ResultOf<typeof FormFragment>
  mutation: UseMutationExecute
}) {
  if (form == null || mutation == null) return

  return (
    <EbanaSimpleForm
      form={form}
      mutation={mutation}
      render={({ isSubmitting }) => {
        return (
          <IconButton variant='plain' aria-label='delete file' type='submit' loading={isSubmitting}>
            <DeleteIcon />
          </IconButton>
        )
      }}
    />
  )
}

function DownloadFile({ url, name }: Pick<Props, 'url' | 'name'>) {
  return (
    <Link target='_blank' href={url} download={name}>
      <IconButton background='transparent' alignItems='flex-start' aria-label='download file button'>
        <Image src='/assets/download.svg' height='100%' alt='download' />
      </IconButton>
    </Link>
  )
}
