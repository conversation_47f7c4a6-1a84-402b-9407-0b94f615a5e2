import { Input as ChakraInput } from '@chakra-ui/react'
import { RegisterOptions, useFormContext } from 'react-hook-form'
import { useFormField } from './FormField'

export function TextInput() {
  const field = useFormField()
  const { path, status, statusMessage, pattern, patternMessage } = field

  const { register } = useFormContext()
  const options: RegisterOptions = {}

  const isRequired = status === 'REQUIRED'
  options.required = {
    value: isRequired,
    message: isRequired ? statusMessage : null,
  }

  options.disabled = status === 'DISABLED'

  options.pattern = {
    value: new RegExp(pattern),
    message: patternMessage,
  }

  options.setValueAs = (v) => (!v || !v.trim() ? null : v)

  return <ChakraInput {...register(path, options)} />
}
