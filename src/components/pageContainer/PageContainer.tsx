'use client'

import { Box, BoxProps } from '@chakra-ui/react'
import React from 'react'
import { PageDesignContext } from '../Navigation'

export type PageContainerProps = {
  /** we should replace @type {any} to HeaderProps */
  Header?: React.ReactElement<any>
  headerProps?: BoxProps
  contentProps?: BoxProps
} & BoxProps
export default function PageContainer(props: PageContainerProps) {
  const { children, Header, headerProps, contentProps, ...rest } = props

  const { variant = 'float' } = React.useContext(PageDesignContext)

  if (variant === 'normal') {
    return (
      <Box
        mx={0}
        width='-webkit-fill-available'
        style={{ width: '-moz-available' }}
        paddingTop={{ base: '2em', md: 0 }}
        paddingBottom='3em'
        {...rest}>
        {children}
      </Box>
    )
  }

  const horizontalPadding = { base: '1em', md: '2.5em' }

  return (
    <Box transform='translateY(-9.1em)' {...rest}>
      <Box
        as='header'
        width='100%'
        px={horizontalPadding}
        pb='5em'
        pt='11.6em'
        backgroundImage={{
          base: 'url(/assets/page-header-bg-mobile.png)',
          md: 'url(/assets/page-header-bg.png)',
        }}
        backgroundRepeat='no-repeat'
        backgroundSize={{ base: 'cover', sm: 'cover' }}
        {...headerProps}>
        {Header}
      </Box>
      <Box
        px={horizontalPadding}
        mx={0}
        transform='translateY(-2.5em)'
        width='-webkit-fill-available'
        style={{ width: '-moz-available' }}
        paddingBottom='3em'
        {...contentProps}>
        {children}
      </Box>
    </Box>
  )
}
