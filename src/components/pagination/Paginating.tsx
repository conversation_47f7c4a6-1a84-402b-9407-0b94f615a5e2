import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Button, ButtonGroup, Flex, IconButton, Input, Pagination, Spinner } from '@chakra-ui/react'
import React from 'react'
import { HiChe<PERSON>ronLeft, HiChevronRight } from 'react-icons/hi'

const Paginating = ({ pageSize, page, next, prev, goTo, pagesCount, loading }) => {
  const { t } = useEbanaLocale()
  const ref = React.useRef<HTMLInputElement>(null)
  return (
    <>
      <Flex flexWrap='wrap' gap='1.875em' alignItems='center'>
        <Pagination.Root count={pagesCount} pageSize={1} page={page}>
          <ButtonGroup variant='ghost' size='sm'>
            <Pagination.PrevTrigger _rtl={{ transform: 'scaleX(-1)' }} disabled={page === 0} >
              <IconButton onClick={prev} disabled={page === 0}>
                <HiChevronLeft  />
              </IconButton>
            </Pagination.PrevTrigger>

            <Pagination.Items
              render={(pagPage) => (
                <IconButton
                  onClick={() => {
                    goTo(pagPage.value - 1)
                  }}
                  maxHeight='3.2em'
                  minHeight='3.2em'
                  minWidth='3.2em'
                  bg={pagPage.value === page + 1 ? '#00263A' : 'white'}
                  color={pagPage.value === page + 1 ? 'white' : '#00263A'}
                  maxWidth='3.2em'
                  borderWidth='1px'
                  borderColor='#DADDE4'
                  p='1em'
                  variant={{ base: 'ghost', _selected: 'plain' }}>
                  {loading && pagPage.value === page + 1 ? <Spinner /> : pagPage.value}
                </IconButton>
              )}
            />

            <Pagination.NextTrigger _rtl={{ transform: 'scaleX(-1)' }} >
              <IconButton onClick={next} disabled={page === pagesCount - 1}>
                <HiChevronRight />
              </IconButton>
            </Pagination.NextTrigger>
          </ButtonGroup>
        </Pagination.Root>

        <Flex gap='0.5em' alignItems='center' ms=''>
          <Box color='text.typo1' textStyle='body4'>
            {t('page')}
          </Box>
          <Input
            ref={ref}
            width='3.125em'
            height='2.4rem'
            borderRadius='7px'
            px='0'
            ps='0.8571428571em'
            color='text.typo3'
            fontWeight={600}
            textStyle='body4'
            bg='background.container'
            borderColor='border.box'
          />
          <Button
            variant='plain'
            textStyle='body4'
            fontWeight={700}
            color='primary.200'
            lineHeight={1.6428571429}
            onClick={() => {
              goTo(Number(ref.current.value) - 1)
            }}>
            {t('go')}
          </Button>
        </Flex>
      </Flex>
    </>
  )
}

export default Paginating
