import React, { ReactElement, use } from 'react'

export type PageRequest = {
  page: number
  pageSize: number
}

export type Page = {
  totalPages: number
  totalElements: number
  size: number
  number: number
}

type PaginationContextParameters = {
  request?: PageRequest
  setRequest: (request: PageRequest) => void
  page: Page
}

type PaginationContextReturn = {
  next: () => void
  prev: () => void
  goTo: (page: number) => void
  changePageSize: (page: number) => void
  totalPages: number
  totalElements: number
  size: number
  number: number
  loading: boolean
}

const PaginationContextPrivate = React.createContext(null as PaginationContextParameters)

export function PaginationContext(props: PaginationContextParameters & { children: ReactElement }) {
  const { children, setRequest, page, request } = props
  return (
    <PaginationContextPrivate.Provider value={{ setRequest, page, request }}>
      {children}
    </PaginationContextPrivate.Provider>
  )
}

export function usePagination(): PaginationContextReturn {
  const { page, request, setRequest } = use(PaginationContextPrivate)
  const next = () => {
    if (page.number < page.totalPages) {
      setRequest({ pageSize: request.pageSize, page: request.page + 1 })
    }
  }

  const prev = () => {
    if (page.number >= 1) {
      setRequest({ pageSize: request.pageSize, page: request.page - 1 })
    }
  }

  const goTo = (p: number) => {
    if (p >= 0 && p <= page.totalPages) {
      setRequest({ pageSize: request.pageSize, page: p })
    }
  }

  const changePageSize = (size: number) => {
    setRequest({ pageSize: size, page: request.page })
  }

  return {
    goTo,
    changePageSize,
    prev,
    next,
    ...page,
    loading: false,
  }
}
