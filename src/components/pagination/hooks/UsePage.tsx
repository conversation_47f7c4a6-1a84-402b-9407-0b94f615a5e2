import { useState } from 'react'
import { DEFAULT_PAGE_SIZE } from '../constants'
import { PageRequest } from '../PaginationContext'

type UsePageReturn = {
  request: PageRequest
  setRequest: (request: PageRequest) => void
}

export function usePage(): UsePageReturn {
  const [page, setPage] = useState(0)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)

  const setRequest = (request: PageRequest) => {
    setPageSize(request.pageSize)
    setPage(request.page)
  }

  return {
    request: {
      page,
      pageSize,
    },
    setRequest,
  }
}
