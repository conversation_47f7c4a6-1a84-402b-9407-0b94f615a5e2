import { useRouter, useSearchParams } from 'next/navigation'
import { DEFAULT_PAGE_SIZE } from '../constants'
import { PageRequest } from '../PaginationContext'

type UsePageReturn = {
  request: PageRequest
  setRequest: (request: PageRequest) => void
}

export function usePageWithParam(): UsePageReturn {
  const router = useRouter()
  const searchParams = useSearchParams()

  const page = parseInt(searchParams.get('page') || '0', 10) || 0

  const pageSize = parseInt(searchParams.get('pageSize') || DEFAULT_PAGE_SIZE.toString(), 10) || DEFAULT_PAGE_SIZE

  function updateQueryParams(newParams: Record<string, string | number>) {
    const params = new URLSearchParams(searchParams.toString())
    Object.entries(newParams).forEach(([key, value]) => {
      params.set(key, value.toString())
    })
    router.push(`?${params.toString()}` as any)
  }

  const setRequest = (request: PageRequest) => {
    updateQueryParams(request)
  }

  return {
    request: {
      page,
      pageSize,
    },
    setRequest,
  }
}
