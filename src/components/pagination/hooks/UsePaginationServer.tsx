import { get } from 'lodash'
import { useRouter, useSearchParams } from 'next/navigation'
import { useState } from 'react'
import { useQuery } from 'urql'
import { DEFAULT_PAGE_SIZE } from "../constants"
import { UseServerPaginationQueryProps, UseServerPaginationResult } from "../types"

interface ExtendedProps<T> extends UseServerPaginationQueryProps<T> {
  useQueryParams?: boolean // Toggle to use query params or not
}

export function useServerPagination<T>({
  query,
  variables,
  paginatedPath,
  useQueryParams = true,
}: ExtendedProps<T>): UseServerPaginationResult<T> {
  const router = useRouter()
  const searchParams = useSearchParams()

  // Local state (used when useQueryParams is false)
  const [pageState, setPageState] = useState(1)
  const [pageSizeState, setPageSizeState] = useState(DEFAULT_PAGE_SIZE)

  // Resolved values based on toggle
  const page = useQueryParams
    ? parseInt(searchParams.get('page') || '1', 10)
    : pageState

  const pageSize = useQueryParams
    ? parseInt(searchParams.get('pageSize') || DEFAULT_PAGE_SIZE.toString(), 10)
    : pageSizeState

  const response = useQuery<T>({
    query,
    variables: {
      ...variables,
      page,
      pageSize,
    },
  })

  const [{ data }] = response
  const paginatedField = get(data, paginatedPath.join('.'))
  const totalElements = paginatedField?.totalElements || 0
  const pagesCount = Math.ceil(totalElements / pageSize)

  function updateQueryParams(newParams: Record<string, string | number>) {
    const params = new URLSearchParams(searchParams.toString())
    Object.entries(newParams).forEach(([key, value]) => {
      params.set(key, value.toString())
    })
    router.push(`?${params.toString()}` as any)
  }

  // Setter wrappers
  const setPage = (p: number) => {
    if (Number.isInteger(p) && p >= 1 && p <= pagesCount) {
      useQueryParams ? updateQueryParams({ page: p }) : setPageState(p)
    }
  }

  const setPageSize = (size: number) => {
    if (useQueryParams) {
      updateQueryParams({ pageSize: size, page: 1 })
    } else {
      setPageSizeState(size)
      setPageState(1)
    }
  }

  const next = () => {
    if (page < pagesCount) {
      setPage(page + 1)
    }
  }

  const prev = () => {
    if (page >= 1) {
      setPage(page - 1)
    }
  }

  const goTo = (p: number) => {
    setPage(p)
  }

  const changePageSize = (size: number) => {
    setPageSize(size)
  }

  return {
    response,
    page,
    pageSize,
    next,
    prev,
    goTo,
    changePageSize,
    totalElements,
    pagesCount,
    loading: response[0].fetching,
  }
}
