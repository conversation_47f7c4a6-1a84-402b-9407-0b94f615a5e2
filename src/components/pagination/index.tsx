import { Flex } from '@chakra-ui/react'
import Paginating from './Paginating'
import { usePagination } from './PaginationContext'
import Paginator from './paginator'
import { UseClientPaginationResult, UseServerPaginationResult } from './types'

export function Pagination<T>(
  props: Omit<UseServerPaginationResult<never>, 'response'> & Omit<UseClientPaginationResult<T>, 'list'>
) {
  const { page, pageSize, changePageSize, next, prev, goTo, totalElements, pagesCount, loading } = props

  if (totalElements <= pageSize && page === 0) return

  return (
    <Flex justifyContent='space-between' gap='2em' flexWrap='wrap'>
      <Paginator pageSize={pageSize} setPageSize={changePageSize} page={page} totalElements={totalElements} />
      <Paginating
        pageSize={pageSize}
        page={page}
        next={next}
        prev={prev}
        goTo={goTo}
        pagesCount={pagesCount}
        loading={loading}
      />
    </Flex>
  )
}

export function EbanaPagination() {
  const params = usePagination()
  return (
    <Pagination
      page={params.number}
      pageSize={params.size}
      pagesCount={params.totalPages}
      next={params.next}
      prev={params.prev}
      loading={params.loading}
      {...params}
    />
  )
}
