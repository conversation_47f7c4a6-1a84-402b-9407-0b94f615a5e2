import { Box, Flex } from '@chakra-ui/react'

import { useTranslation } from "@/app/i18n/client"
import { ApplicationPropsContext } from "@/context/application-props"
import { Select } from 'chakra-react-select'
import { useContext, useMemo } from "react"
import { PAGE_SIZE_OPTIONS } from './constants'

type PaginatorProps = { 
    pageSize: number
    setPageSize: (pageSize: number) => void
    page: number
    totalElements: number
}

const  Paginator: React.FC<PaginatorProps> =({ pageSize, setPageSize, page, totalElements })=> {
    const { lng } =  useContext(ApplicationPropsContext)
    const { t } = useTranslation(lng, 'Pages')
  
    const pageNumber = page + 1
  
    const from = useMemo(() => (pageNumber === 1 ? 1 : pageSize * (pageNumber - 1) + 1), [pageNumber, pageSize])
    const to = useMemo(() => Math.min(pageSize * pageNumber, totalElements), [pageNumber, pageSize, totalElements])
  
    return (
      <Flex gap='1.5625em' alignItems='center'>
        <Flex gap='0.5em' alignItems='center'>
          <Box color='text.typo1' textStyle='body4' fontWeight={600} whiteSpace="nowrap">
            {t('rows_per_page')}
          </Box>
          <Select
            onChange={(v) => setPageSize(v.value)}
            value={PAGE_SIZE_OPTIONS.find((opt) => opt.value === pageSize) || null}
            chakraStyles={{
              control: (styles) => ({
                ...styles,
                borderRadius: '7px',
                minHeight: '2.6em',
                height: '2.7em',
                padding: '0 0.6428571429em',
                bg: 'white',
              }),
              valueContainer: (styles) => ({
                ...styles,
                p: 0,
              }),
              dropdownIndicator: (styles) => ({
                ...styles,
                ps: 0,
              }),
            }}
            placeholder=''
            options={PAGE_SIZE_OPTIONS}
          />
        </Flex>
        <Box color='text.typo1' textStyle='body4'>
          {`${from} - ${to > totalElements ? totalElements : to} ${t('of')} ${totalElements} ${t('items')}`}
        </Box>
      </Flex>
    )
  }

  export default   Paginator