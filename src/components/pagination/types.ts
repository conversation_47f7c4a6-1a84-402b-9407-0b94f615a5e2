import { TadaDocumentNode } from "gql.tada"
import { UseQueryResponse } from "urql"

export type UseServerPaginationQueryProps<T> = {
    query: TadaDocumentNode<T>
    variables: any
    paginatedPath: string[]
  }
  
  export type PaginationInfo = {
    page: number
    pageSize: number
    next: () => void
    prev: () => void
    goTo: (page: number) => void
    changePageSize: (page: number) => void
    pagesCount: number
    totalElements: number
  }
  
  export type UseServerPaginationResult<T> = {
    response: UseQueryResponse<T>
    loading?: boolean
  } & PaginationInfo
  
  export type UseClientPaginationResult<T> = {
    list: T[]
  } & PaginationInfo
  