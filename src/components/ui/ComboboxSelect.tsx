'use client'

import { Badge, Combobox, Portal, Wrap, createListCollection } from '@chakra-ui/react'
import { useMemo } from 'react'

export type ComboboxFieldOption<T = any> = {
  label: string
  value: T
}

type CustomComboboxProps<T> = {
  label: string
  renderBadge?: (value: T, label: string) => React.ReactNode
  placeholder?: string
  options: ComboboxFieldOption<T>[]
  onSearchChange?: (search: string) => void
} & Combobox.RootProps

export function CustomCombobox<T>({
  label,
  value,
  options,
  onSearchChange,
  renderBadge,
  placeholder = 'Search...',
  width = '100%',
  multiple = true,
}: CustomComboboxProps<T>) {
  const collection = useMemo(() => createListCollection({ items: options }), [options])

  const handleInputChange = (details: Combobox.InputValueChangeDetails) => {
    onSearchChange?.(details.inputValue)
  }

  // Normalize value to array
  const valueArray = multiple && Array.isArray(value) ? value : value !== undefined && value !== null ? value : []

  return (
    <Combobox.Root
      multiple={multiple}
      closeOnSelect
      value={value}
      collection={collection}
      onInputValueChange={handleInputChange}>
      <Wrap mb={2}>
        {valueArray?.map((v, index) => {
          const labelText = options.find((opt) => opt.value === v)?.label ?? String(v)
          return renderBadge ? (
            renderBadge(v as any, labelText)
          ) : (
            <Badge key={index} colorScheme='teal' px={2} py={1} borderRadius='md'>
              {labelText}
            </Badge>
          )
        })}
      </Wrap>

      <Combobox.Label mb={1}>{label}</Combobox.Label>

      <Combobox.Control>
        <Combobox.Input placeholder={placeholder} />
        <Combobox.IndicatorGroup>
          <Combobox.Trigger />
        </Combobox.IndicatorGroup>
      </Combobox.Control>

      <Portal>
        <Combobox.Positioner zIndex={10}>
          <Combobox.Content>
            <Combobox.ItemGroup>
              {options.length > 0 ? (
                options.map((item) => (
                  <Combobox.Item key={JSON.stringify(item.value)} item={item.value}>
                    {item.label}
                    <Combobox.ItemIndicator />
                  </Combobox.Item>
                ))
              ) : (
                <Combobox.Empty>No results found</Combobox.Empty>
              )}
            </Combobox.ItemGroup>
          </Combobox.Content>
        </Combobox.Positioner>
      </Portal>
    </Combobox.Root>
  )
}
