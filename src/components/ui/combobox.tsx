import { Portal } from '@chakra-ui/react'
import {
  Combobox as <PERSON>kraCombobox,
  ComboboxContentProps as ChakraComboboxContentProps,
  ComboboxInputProps as ChakraComboboxInputProps,
} from '@chakra-ui/react/combobox'
import React from 'react'

type ComboboxInputProps = {
  clearTrigger?: boolean
  trigger?: boolean
} & ChakraComboboxInputProps

export const ComboboxInput = React.forwardRef<HTMLInputElement, ComboboxInputProps>(function ComboboxInput(
  props: ComboboxInputProps,
  ref
) {
  const { clearTrigger = true, trigger = true, ...inputProps } = props

  return (
    <ChakraCombobox.Control>
      <ChakraCombobox.Input ref={ref} {...inputProps} />
      {!clearTrigger && !trigger && (
        <ChakraCombobox.IndicatorGroup>
          {clearTrigger && <ChakraCombobox.ClearTrigger />}
          {trigger && <ChakraCombobox.Trigger />}
        </ChakraCombobox.IndicatorGroup>
      )}
    </ChakraCombobox.Control>
  )
})

export type ComboboxContentProps = {
  empty?: React.ReactNode
  children: React.ReactElement
} & ChakraComboboxContentProps

export const ComboboxContent = React.forwardRef<HTMLDivElement, ComboboxContentProps>(
  function ComboboxContent(props, ref) {
    const { empty, children, ...rest } = props
    return (
      <Portal>
        <ChakraCombobox.Positioner>
          <ChakraCombobox.Content ref={ref} {...rest}>
            {empty && <ChakraCombobox.Empty>{empty}</ChakraCombobox.Empty>}
            {children}
          </ChakraCombobox.Content>
        </ChakraCombobox.Positioner>
      </Portal>
    )
  }
)

export const ComboboxRoot = ChakraCombobox.Root
export const ComboboxItem = ChakraCombobox.Item
export const ComboboxIndicator = ChakraCombobox.Indicator
export const ComboboxItemGroup = ChakraCombobox.ItemGroup
