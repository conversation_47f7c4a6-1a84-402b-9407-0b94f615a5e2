import PageContainer from '@/components/pageContainer/PageContainer'
import PageHeader from '@/components/PageHeader'
import { useAuth } from '@/context/new-auth'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Button, Grid, Text } from '@chakra-ui/react'
import NextLink from 'next/link'
import { route } from 'nextjs-routes'
import React from 'react'

export default function MoyserPaymentSuccessPage({ lng, status, companyId, payFor }) {
  const { t } = useEbanaLocale()

  const { homePage } = useAuth()
  const [loading, setLoading] = React.useState(false)

  return (
    <PageContainer Header={<PageHeader t={t} pageTitle='' />}>
      <Grid gridTemplateColumns='65% calc(35% - 1.25em)' gridRowGap='1.25em' gridColumnGap='1.25em'>
        <Box gridColumn='1 / -1' gridRow='1 / 2'>
          <section style={{ minHeight: '100%' }}>
            <Box background='background.400' py='1.5625em' px='1.875em' borderRadius='14px' minHeight='100%'>
              <Box textAlign='center' my='10em'>
                {status === 'success' ? (
                  <>
                    <Box my='0.5em' textStyle='h2'>
                      {t('payment-submit-successfully-msg1')}
                    </Box>
                    {getPaymentAction()}
                  </>
                ) : (
                  <>
                    <Box my='0.5em' textStyle='h1'>
                      {t('payment-submit-error-msg1')}
                    </Box>
                    <Text>{t('payment-submit-error-msg2')}</Text>
                    <Button asChild my='1em' loading={loading} onClick={() => setLoading(true)}>
                      <NextLink href={route({ pathname: '/[lng]/individual/home', query: { lng } })}>
                        {t('home')}
                      </NextLink>
                    </Button>
                  </>
                )}
              </Box>
            </Box>
          </section>
        </Box>
      </Grid>
    </PageContainer>
  )

  function getPaymentAction() {
    if (payFor === 'addon') {
      return (
        <>
          <Text>{t('payment-submit-successfully-for-addon-msg2')}</Text>
          <Button asChild my='1em'>
            <NextLink href={homePage}>{t('home')}</NextLink>
          </Button>
        </>
      )
    } else {
      return (
        <>
          <Text>{t('payment-submit-successfully-msg2')}</Text>
          <Button my='1em'>
            <NextLink href={homePage}>{t('home')}</NextLink>
          </Button>
        </>
      )
    }
  }
}
