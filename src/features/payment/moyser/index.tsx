import { ApplePayIcon } from '@/components/EbanaIcons'
import CardLoading from '@/components/loading/Card'
import { Checkbox } from '@/components/ui/checkbox'
import { RadioCardItem, RadioCardItemIndicator, RadioCardRoot } from '@/components/ui/radio-card'
import { toaster } from '@/components/ui/toaster'
import { paymentFor } from '@/constants/pyament'
import customAxios from '@/endpoints/axiosInterceptors'
import {
  addCard,
  completePayment,
  createAddonPayment,
  createStockRequestPayment,
  createSubscriptionPayment,
  initiatePayment,
  moyserPay,
} from '@/endpoints/payment'
import { useError } from '@/hooks/error'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Button, Field, Flex, Image, Input, Text } from '@chakra-ui/react'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { route } from 'nextjs-routes'
import { useImperativeHandle, useState } from 'react'
import { usePaymentInputs } from 'react-payment-inputs'
import ApplePayButton from '../applepay'
import { DirectTradeInfo } from './DirectTradeInfo'
import styles from './moyser.module.css'

export function MoyserPaymentPage(props) {
  const { lng, orderId, payFunctionRef, isPreEmption, payFor, companyId, metadata = null } = props

  const { t } = useEbanaLocale()
  const error = useError(lng)
  const errors = error?.errors as any
  const [formData, setFormData] = useState({
    cardNumber: '',
    expDate: '',
    name: '',
    cvv: '',
    saveCard: false,
  })
  const [createPaymentIsLoading, setCreatePaymentIsLoading] = useState(true)
  const [submitted, setSubmitted] = useState(false)
  const [selectedCard, setSelectedCard] = useState(null)
  const { meta, getCardNumberProps, getExpiryDateProps, getCVCProps } = usePaymentInputs({
    errorMessages: {
      emptyCardNumber: t('cardNumber-required'),
      invalidCardNumber: t('invalidCardNumber'),
      emptyExpiryDate: t('expDate-required'),
      monthOutOfRange: t('monthOutOfRange'),
      yearOutOfRange: t('yearOutOfRange'),
      dateOutOfRange: t('dateOutOfRange'),
      invalidExpiryDate: t('invalidExpiryDate'),
      emptyCVC: t('cvv-required'),
      invalidCVC: t('invalidCVC'),
    },
  })

  const router = useRouter()

  useImperativeHandle(payFunctionRef, () => ({
    pay() {
      return validateAndPay()
    },
  }))

  async function createPayment() {
    if (payFor === paymentFor.SUBSCRIPTION) {
      return createSubscriptionPayment(
        orderId,
        companyId,
        `/${lng}/payment-success?payFor=subscription&companyId=${companyId}`
      )
    } else if (payFor === paymentFor.ADDON) {
      return createAddonPayment(orderId, companyId, `/${lng}/payment-success`)
    } else if (payFor === paymentFor.ADD_CARD) {
      return addCard(companyId, `/${lng}/company/${companyId}/plan-management`)
    } else {
      return await createStockRequestPayment(orderId, `/${lng}/payment-success?payFor=stockRequest`)
    }
  }

  const { data, isLoading, isFetching, isError } = useQuery({
    // queryKey: ['create-payment', orderId ? orderId : `add-card`],
    queryKey: [payFor],
    queryFn: () =>
      createPayment().then(
        (res) => {
          setCreatePaymentIsLoading(false)
          return res
        },
        (err) => {
          setCreatePaymentIsLoading(false)
          error.showServerError(error)
        }
      ),
    refetchOnWindowFocus: false,
    retry: false,
    enabled: !!orderId || payFor === paymentFor.ADD_CARD,
  })

  const paymentRequest = data as any

  // if (payFor !== paymentFor.ADD_CARD) {
  //   window.onApplePayButtonClicked = function () {
  //     if (!ApplePaySession) {
  //       return
  //     }

  //     // Define ApplePayPaymentRequest

  //     // Starting the Apple Pay Session
  //     const session = new ApplePaySession(3, paymentRequest?.data?.apple?.request)

  //     // Validating Merchant
  //     session.onvalidatemerchant = async (event) => {
  //       // Calling Moyasar's servers to initiate a new Merchant Validation Seaaion.

  //       const body = {
  //         validation_url: event.validationURL,
  //         ...paymentRequest?.data?.apple?.initiate,
  //       }

  //       fetch(paymentRequest?.data.apple?.initiateUrl, {
  //         method: 'post',
  //         headers: { 'Content-Type': 'application/json' },
  //         body: JSON.stringify(body),
  //       })
  //         .then((res) => res.json()) // Parse response as JSON.
  //         .then((merchantSession) => {
  //           session.completeMerchantValidation(merchantSession)
  //         })
  //         .catch((err) => {
  //           console.error('Error fetching merchant session', err)
  //           toaster.create({
  //             title: t('GENERAL_ERROR'),
  //             type: 'error',
  //             closable: true,
  //           })
  //         })
  //     }

  //     session.onpaymentauthorized = (event) => {
  //       const token = event.payment.token

  //       // prepare request for moyasar
  //       let body = paymentRequest?.data?.apple.payment
  //       body.source.token = token

  //       // send the request
  //       fetch(paymentRequest?.data.apple?.paymentUrl, {
  //         method: 'post',
  //         headers: { 'Content-Type': 'application/json' },
  //         body: JSON.stringify(body),
  //       })
  //         .then((response) => response.json())
  //         .then((payment) => {
  //           if (!payment.id) {
  //             toaster.create({
  //               title: t('GENERAL_ERROR'),
  //               type: 'error',
  //               closable: true,
  //             })
  //           }

  //           if (payment.status != 'paid') {
  //             session.completePayment({
  //               status: ApplePaySession.STATUS_FAILURE,
  //               errors: [payment.source.message],
  //             })

  //             toaster.create({
  //               title: payment.source.message,
  //               type: 'error',
  //               closable: true,
  //             })
  //             return
  //           }

  //           completePayment(paymentRequest.data.id, payment.id).then(
  //             (res) => {
  //               session.completePayment({
  //                 status: ApplePaySession.STATUS_SUCCESS,
  //               })
  //               window.location = `/${lng}/payment-success?status=success`
  //             },
  //             (err) => {
  //               session.completePayment({
  //                 status: ApplePaySession.STATUS_SUCCESS,
  //               })
  //               window.location = `/${lng}/payment-success?status=success`
  //             }
  //           )
  //         })
  //         .catch((error) => {
  //           session.completePayment({
  //             status: ApplePaySession.STATUS_FAILURE,
  //             errors: [],
  //           })
  //           toaster.create({
  //             title: error.toString(),
  //             type: 'error',
  //             closable: true,
  //           })
  //         })
  //     }

  //     session.begin()
  //   }
  // }

  const onApplePayClick = function () {
    // Define ApplePayPaymentRequest

    // Starting the Apple Pay Session
    const session = new ApplePaySession(3, paymentRequest?.data?.apple?.request)

    handleEventsForApplePay(session, lng, t('GENERAL_ERROR'), paymentRequest.data)

    session.begin()
  }

  return createPaymentIsLoading ? (
    getLoadingContent()
  ) : (
    <>
      {payFor === paymentFor.STOCK_REQUEST && (
        <DirectTradeInfo id={orderId} totalAmount={paymentRequest?.data?.amountResidual} />
      )}
      {getPaymentContent()}
    </>
  )

  function getLoadingContent() {
    return <CardLoading numberOfCards={1} numberOfCol={1} />
  }

  function getPaymentContent() {
    let brandToFile = {
      VISA: '/assets/visa.svg',
      MADA: '/assets/mada.svg',
      MASTER: '/assets/mastercard.svg',
    }

    return (
      <Box width={{ base: '100%', md: '100%' }}>
        {!isPreEmption && !isLoading && !isFetching && !isError ? (
          <>
            {payFor !== paymentFor.ADD_CARD && (
              <>
                <Box color='#323B41' textStyle='h3' fontWeight={600}>
                  {t('payment_method')}
                </Box>

                <RadioCardRoot
                  onValueChange={(value) => {
                    setSelectedCard(
                      paymentRequest?.data?.tokenizedCards?.find((card) => card.id === value.value) || value
                    )
                  }}>
                  {paymentRequest?.data?.tokenizedCards.map((it) => (
                    <RadioCardItem
                      key={it.id}
                      value={it.id}
                      p='1.1rem'
                      width='100%'
                      height='88px'
                      minHeight='88px'
                      maxHeight='88px'
                      _hover={{
                        backgroundColor: '#FAFBFC',
                        transition: 'all 0.25s ease',
                      }}
                      mt='1em'
                      borderWidth='1px'
                      borderColor={selectedCard === 'cards' ? 'primary.200' : 'stroke.100'}
                      borderRadius='0.875em'
                      alignItems='center'
                      _checked={{
                        borderColor: 'primary.200',
                        outline: 'none',
                        boxShadow: 'none',
                      }}
                      label={
                        <Flex
                          width={{ base: '80vw', md: '600px' }}
                          // justifyContent='space-between'
                          alignItems='center'
                          gap='12px'>
                          <RadioCardItemIndicator _checked={{ borderColor: 'transparent', bg: 'primary.200' }} />
                          <Box
                            bgColor='white'
                            height='32px'
                            p='5px'
                            borderWidth='1px'
                            borderColor='#F2F4F7'
                            borderRadius={8}
                            width='46px'
                            overflow='hidden'
                            justifyContent='center'
                            alignContent='center'>
                            <Image src={brandToFile[it.brand]} alt='' />
                          </Box>
                          <Box>
                            <Box mt='0.50em' textStyle='h6' fontWeight={600}>
                              {it.brand.charAt(0).toUpperCase() + it.brand.slice(1).toLowerCase()} {t('ending-in')}{' '}
                              {it.lastFour}
                            </Box>
                            <Box textStyle='body4' color='typography.400'>
                              {t('expiry')} {it.month}/{it.year}
                            </Box>
                          </Box>
                        </Flex>
                      }
                    />
                  ))}

                  <Box
                    borderRadius='14px'
                    borderWidth='1px'
                    mt='1em'
                    backgroundColor={selectedCard?.value === 'cards' ? '#FAFBFC' : 'transparent'}
                    borderColor={selectedCard?.value === 'cards' ? 'primary.200' : 'transparent'}>
                    <RadioCardItem
                      onSelect={() => {
                        setSelectedCard('cards')
                      }}
                      px='1rem'
                      label={
                        <Flex
                          width={{ base: '80vw', md: '600px' }}
                          height='fit-content'
                          gap='5px'
                          alignItems='center'
                          pt='1.5rem'>
                          <RadioCardItemIndicator _checked={{ borderColor: 'transparent', bg: 'primary.200' }} />
                          <Box ms='0.5rem' width='100%' textStyle='body2' fontWeight={600} color='typography.100'>
                            {t('credit_or_debit')}
                          </Box>
                          <Box
                            bgColor='white'
                            height='32px'
                            p='5px'
                            borderWidth='1px'
                            borderColor='#F2F4F7'
                            borderRadius={8}
                            width='46px'
                            overflow='hidden'
                            justifyContent='center'
                            alignContent='center'>
                            <Image src='/assets/visa.svg' alt='' />
                          </Box>
                          <Box
                            bgColor='white'
                            height='32px'
                            p='5px'
                            borderWidth='1px'
                            borderColor='#F2F4F7'
                            borderRadius={8}
                            width='46px'
                            overflow='hidden'
                            justifyContent='center'
                            alignContent='center'>
                            <Image src='/assets/mada.svg' alt='' />
                          </Box>
                          <Box
                            bgColor='white'
                            height='32px'
                            p='5px'
                            pt='2px'
                            borderWidth='1px'
                            borderColor='#F2F4F7'
                            borderRadius={8}
                            width='46px'
                            overflow='hidden'
                            justifyContent='center'
                            alignContent='center'>
                            <Image src='/assets/mastercard.svg' alt='' />
                          </Box>
                        </Flex>
                      }
                      value='cards'
                      width='100%'
                      minHeight='88px'
                      _hover={{
                        backgroundColor: '#FAFBFC',
                        transition: 'all 0.25s ease',
                      }}
                      borderWidth='1px'
                      borderColor='stroke.100'
                      borderRadius='0.875em'
                      alignItems='center'
                      _checked={{
                        borderColor: 'transparent',
                        outline: 'none',
                        boxShadow: 'none',
                      }}
                    />

                    {selectedCard?.value === 'cards' && (
                      <Box width='95%' p='30px' mx='2.5%' mt='0em' bg='white' borderRadius='14px' mb='1rem'>
                        <Field.Root>
                          <Field.Label>{t('name')}*</Field.Label>
                          <Input
                            backgroundColor='background.300'
                            placeholder={t('enter-cardholder-name')}
                            onChange={(event) =>
                              setFormData({
                                ...formData,
                                name: event.target.value,
                              })
                            }></Input>
                          <Field.ErrorText>
                            {t(errors?.name?.msg ? errors?.name?.msg : 'name-required')}
                          </Field.ErrorText>
                        </Field.Root>

                        <Field.Root mt='1.875em'>
                          <Field.Label>{t('cardNumber')}*</Field.Label>
                          <Input
                            className={
                              styles.cinput +
                              ' ' +
                              (meta?.cardType?.type === 'visa' ? styles.visaLogo : '') +
                              ' ' +
                              (meta?.cardType?.type === 'mastercard' ? styles.mastercardLogo : '')
                            }
                            {...getCardNumberProps({
                              onChange: (event) =>
                                setFormData({
                                  ...formData,
                                  cardNumber: event.target.value,
                                }),
                              placeholder: t('cardNumber-placeholder'),
                            })}
                            value={formData.cardNumber}
                          />
                          <Field.ErrorText>
                            {t(errors?.cardNumber?.msg ? errors?.cardNumber?.msg : 'cardNumber-required')}
                          </Field.ErrorText>
                          {(meta.isTouched || submitted) && meta.erroredInputs && meta.erroredInputs.cardNumber && (
                            <Text mt='0.5rem' color='red'>
                              {meta.erroredInputs.cardNumber}
                            </Text>
                          )}
                        </Field.Root>

                        <Flex mt='1.875em' gap='0.5rem'>
                          <Field.Root invalid={errors?.expDate?.invalid} width='70%'>
                            <Field.Label>{t('expDate')}*</Field.Label>

                            <input
                              className={styles.cinput}
                              {...getExpiryDateProps({
                                onChange: (event) =>
                                  setFormData({
                                    ...formData,
                                    expDate: event.target.value,
                                  }),
                                placeholder: t('expDate-placeholder'),
                              })}
                              value={formData.expDate}
                            />
                            <Field.ErrorText>
                              {t(errors?.expDate?.msg ? errors?.expDate?.msg : 'expDate-required')}
                            </Field.ErrorText>
                            {(meta.isTouched || submitted) && meta.erroredInputs && meta.erroredInputs.expiryDate && (
                              <Text mt='0.5rem' color='red'>
                                {meta.erroredInputs.expiryDate}
                              </Text>
                            )}
                          </Field.Root>
                          <Field.Root invalid={errors?.cvv?.invalid} width='30%'>
                            <Field.Label>{t('cvv')}*</Field.Label>

                            <input
                              className={styles.cinput}
                              {...getCVCProps({
                                onChange: (event) => setFormData({ ...formData, cvv: event.target.value }),
                                placeholder: t('cvv-placeholder'),
                              })}
                              value={formData.cvv}
                            />
                            <Field.ErrorText>
                              {t(errors?.expDate?.msg ? errors?.expDate?.msg : 'cvv-required')}
                            </Field.ErrorText>
                            {(meta.isTouched || submitted) && meta.erroredInputs && meta.erroredInputs.cvc && (
                              <Text mt='0.5rem' color='red'>
                                {meta.erroredInputs.cvc}
                              </Text>
                            )}
                          </Field.Root>
                        </Flex>

                        {payFor !== paymentFor.ADD_CARD && (
                          <Box my='1em'>
                            <Checkbox
                              mt='1rem'
                              inputProps={{
                                onChange: (e) => setFormData({ ...formData, saveCard: e.target.checked }),
                              }}>
                              {t('save_card_for_future')}
                            </Checkbox>
                          </Box>
                        )}
                      </Box>
                    )}
                  </Box>

                  {true ? (
                    <RadioCardItem
                      key='applepay'
                      value='applepay'
                      height='88px'
                      minHeight='88px'
                      maxHeight='88px'
                      alignContent='center'
                      p='1.1rem'
                      _checked={{
                        borderColor: 'primary.200',
                        outline: 'none',
                        boxShadow: 'none',
                      }}
                      _hover={{
                        backgroundColor: '#FAFBFC',
                        transition: 'all 0.25s ease',
                      }}
                      mt='1em'
                      borderWidth='1px'
                      borderColor={selectedCard === 'cards' ? 'primary.200' : 'stroke.100'}
                      borderRadius='0.875em'
                      alignItems='center'
                      label={
                        <Flex
                          pt='0.5rem'
                          width={{ base: '80vw', md: '600px' }}
                          alignSelf='center'
                          justifyContent='space-between'
                          alignContent='center'
                          alignItems='center'>
                          <Flex gap='5px' alignContent='top' alignItems='center'>
                            <RadioCardItemIndicator _checked={{ borderColor: 'transparent', bg: 'primary.200' }} />
                            <Box textStyle='body2' fontWeight={600} color='typography.100'>
                              {t('applepay')}
                            </Box>
                          </Flex>
                          <Box
                            bgColor='white'
                            height='32px'
                            borderWidth='1px'
                            borderColor='#F2F4F7'
                            borderRadius={8}
                            width='46px'
                            overflow='hidden'>
                            <ApplePayIcon mt='4px' ms='7px' width='28px' />
                          </Box>
                        </Flex>
                      }
                    />
                  ) : null}
                </RadioCardRoot>
              </>
            )}
            {(formData as any)?.selectedPaymentType === 'cards' || payFor === paymentFor.ADD_CARD ? (
              <Box mt='1.875em'>
                <Field.Root invalid={errors?.name?.invalid}>
                  <Field.Label>{t('name')}*</Field.Label>
                  <Input
                    placeholder={t('enter-cardholder-name')}
                    onChange={(event) =>
                      setFormData({
                        ...formData,
                        name: event.target.value,
                      })
                    }></Input>
                  <Field.ErrorText>{t(errors?.name?.msg ? errors?.name?.msg : 'name-required')}</Field.ErrorText>
                </Field.Root>

                <Field.Root invalid={errors?.cardNumber?.invalid} mt='1.875em'>
                  <Field.Label>{t('cardNumber')}*</Field.Label>

                  <input
                    className={
                      styles.cinput +
                      ' ' +
                      (meta?.cardType?.type === 'visa' ? styles.visaLogo : '') +
                      ' ' +
                      (meta?.cardType?.type === 'mastercard' ? styles.mastercardLogo : '')
                    }
                    {...getCardNumberProps({
                      onChange: (event) =>
                        setFormData({
                          ...formData,
                          cardNumber: event.target.value,
                        }),
                      placeholder: t('cardNumber-placeholder'),
                    })}
                    value={formData.cardNumber}
                  />
                  <Field.ErrorText>
                    {t(errors?.cardNumber?.msg ? errors?.cardNumber?.msg : 'cardNumber-required')}
                  </Field.ErrorText>
                  {(meta.isTouched || submitted) && meta.erroredInputs && meta.erroredInputs.cardNumber && (
                    <Text mt='0.5rem' color='red'>
                      {meta.erroredInputs.cardNumber}
                    </Text>
                  )}
                </Field.Root>

                <Flex mt='1.875em' gap='0.5rem'>
                  <Field.Root invalid={errors?.expDate?.invalid} width='70%'>
                    <Field.Label>{t('expDate')}*</Field.Label>

                    <input
                      className={styles.cinput}
                      {...getExpiryDateProps({
                        onChange: (event) =>
                          setFormData({
                            ...formData,
                            expDate: event.target.value,
                          }),
                        placeholder: t('expDate-placeholder'),
                      })}
                      value={formData.expDate}
                    />
                    <Field.ErrorText>
                      {t(errors?.expDate?.msg ? errors?.expDate?.msg : 'expDate-required')}
                    </Field.ErrorText>
                    {(meta.isTouched || submitted) && meta.erroredInputs && meta.erroredInputs.expiryDate && (
                      <Text mt='0.5rem' color='red'>
                        {meta.erroredInputs.expiryDate}
                      </Text>
                    )}
                  </Field.Root>
                  <Field.Root invalid={errors?.cvv?.invalid} width='30%'>
                    <Field.Label>{t('cvv')}*</Field.Label>

                    <input
                      className={styles.cinput}
                      {...getCVCProps({
                        onChange: (event) => setFormData({ ...formData, cvv: event.target.value }),
                        placeholder: t('cvv-placeholder'),
                      })}
                      value={formData.cvv}
                    />
                    <Field.ErrorText>{t(errors?.expDate?.msg ? errors?.expDate?.msg : 'cvv-required')}</Field.ErrorText>
                    {(meta.isTouched || submitted) && meta.erroredInputs && meta.erroredInputs.cvc && (
                      <Text mt='0.5rem' color='red'>
                        {meta.erroredInputs.cvc}
                      </Text>
                    )}
                  </Field.Root>
                </Flex>

                {payFor !== paymentFor.ADD_CADR && (
                  <Box my='1em'>
                    <Checkbox
                      inputProps={{
                        onChange: (e) => setFormData({ ...formData, saveCard: e.target.checked }),
                      }}
                      mt='1rem'>
                      {t('save_card_for_future')}
                    </Checkbox>
                  </Box>
                )}
              </Box>
            ) : null}
          </>
        ) : isPreEmption && !isLoading && !isFetching && !isError ? (
          <Box margin='auto'>
            <Text>{t('PreEmptionMsg')}</Text>
          </Box>
        ) : null}

        {payFor !== paymentFor.ADD_CARD && (
          <>
            <Box mt='2rem' width='100%' height='1px' bg='stroke.200' />
            <Flex justifyContent='space-between' mt='1.5625em'>
              {payFor === paymentFor.STOCK_REQUEST && (
                <Button
                  loadingText={t('agree')}
                  onClick={() => router.replace(route({ pathname: '/[lng]/individual/home', query: { lng } }))}
                  variant='secondary'>
                  {t('pay_later')}
                </Button>
              )}

              {selectedCard?.value === 'cards' ? (
                <Button ms='auto' loadingText={t('agree')} onClick={confirmAndPay}>
                  {t('confirm-and-pay')}
                </Button>
              ) : selectedCard?.value === 'applepay' ? (
                <ApplePayButton
                  type='pay'
                  locale='en'
                  buttonStyle='white-outline'
                  onClick={onApplePayClick}
                  style={{
                    // width: '100%',
                    borderRadius: '8px',
                    height: '53px',
                  }}
                />
              ) : selectedCard ? (
                <Box width='fit-content' ms='auto'>
                  <form key={selectedCard.id} method='post' action={selectedCard.payUrl}>
                    <Button ms='auto' type='submit'>
                      {t('confirm-and-pay')}
                    </Button>
                  </form>
                </Box>
              ) : (
                <Button ms='auto' loadingText={t('agree')} onClick={() => {}} disabled>
                  {t('confirm-and-pay')}
                </Button>
              )}
            </Flex>
          </>
        )}
      </Box>
    )
  }

  function confirmAndPay() {
    if (payFunctionRef.current) {
      payFunctionRef.current.pay()
    }
  }

  function validateAndPay() {
    setSubmitted(true)
    error.clearFormErrors()
    if (!formData.name) {
      error.setFormError('name', 'name-required')
      return Promise.resolve(false)
    }
    if (!meta.error) {
      const source = {
        ...paymentRequest?.data?.payment?.source,
        name: formData?.name,
        number: formData?.cardNumber?.replace(/ /g, ''),
        cvc: formData?.cvv,
        month: formData?.expDate?.split('/')[0].replace(/ /g, ''),
        year: formData?.expDate?.split('/')[1].replace(/ /g, ''),
        save_card: (formData?.saveCard ?? false) || payFor === paymentFor.ADD_CARD ? 'true' : 'false',
      }

      const request =
        payFor === paymentFor.ADD_CARD
          ? { ...paymentRequest?.data?.form, ...source }
          : { ...paymentRequest?.data?.payment, source: source }

      return moyserPay(request, paymentRequest.data.paymentUrl).then(
        (mRes) => {
          if (payFor === paymentFor.ADD_CARD) {
            const initiateBody = { token: mRes?.data?.id }
            const nextUrl = mRes?.data?.verification_url

            return customAxios.post(paymentRequest.data.initiateUrl, initiateBody).then(
              (res) => {
                window.location = nextUrl
                return Promise.resolve(true)
              },
              (err) => {
                return Promise.reject(false)
              }
            )
          }
          return initiatePayment(paymentRequest?.data?.id, mRes?.data?.id, mRes?.data?.source?.token).then(
            (res) => {
              window.location = mRes?.data?.source?.transaction_url
              return Promise.resolve(true)
            },
            (err) => {
              return Promise.reject(false)
            }
          )
        },
        (err) => {
          return Promise.reject(false)
        }
      )
    } else {
      return Promise.reject(false)
    }
  }
}

type PaymentRequest = {
  id: string
  apple: {
    request: {}
    payment: object & {
      source: {
        token: ApplePayJS.ApplePayPaymentToken
      }
    }
    initiate: object
    paymentUrl: string
    initiateUrl: string
  }
}

function handleEventsForApplePay(
  session: ApplePaySession,
  lng: string,
  errorMessage: string,
  paymentRequest: PaymentRequest
) {
  // Validating Merchant
  session.onvalidatemerchant = async (event) => {
    // Calling Moyasar's servers to initiate a new Merchant Validation Seaaion.

    const body = {
      validation_url: event.validationURL,
      ...paymentRequest.apple.initiate,
    }

    fetch(paymentRequest.apple.initiateUrl, {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    })
      .then((res) => res.json()) // Parse response as JSON.
      .then((merchantSession) => {
        session.completeMerchantValidation(merchantSession)
      })
      .catch((err) => {
        console.error('Error fetching merchant session', err)
        toaster.create({
          title: errorMessage,
          type: 'error',
          closable: true,
        })
      })
  }

  session.onpaymentauthorized = (event) => {
    const token = event.payment.token

    // prepare request for moyasar
    let body = paymentRequest.apple.payment
    body.source.token = token

    fetch(paymentRequest.apple.paymentUrl, {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    })
      .then((response) => response.json())
      .then((payment) => {
        if (!payment.id) {
          toaster.create({
            title: errorMessage,
            type: 'error',
            closable: true,
          })
        }

        if (payment.status != 'paid') {
          session.completePayment({
            status: ApplePaySession.STATUS_FAILURE,
            errors: [payment.source.message],
          })

          toaster.create({
            title: payment.source.message,
            type: 'error',
            closable: true,
          })
          return
        }

        completePayment(paymentRequest.id, payment.id).then(
          (res) => {
            session.completePayment({
              status: ApplePaySession.STATUS_SUCCESS,
            })
            window.location.href = `/${lng}/payment-success?status=success`
          },
          (err) => {
            session.completePayment({
              status: ApplePaySession.STATUS_SUCCESS,
            })
            window.location.href = `/${lng}/payment-success?status=success`
          }
        )
      })
      .catch((error) => {
        session.completePayment({
          status: ApplePaySession.STATUS_FAILURE,
          errors: [],
        })
        toaster.create({
          title: error.toString(),
          type: 'error',
          closable: true,
        })
      })
  }
}
