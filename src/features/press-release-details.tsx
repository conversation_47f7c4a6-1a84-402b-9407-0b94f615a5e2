import DangerousHtml from '@/components/dangerousHtml'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Center, Flex, Image, Text, VStack } from '@chakra-ui/react'

PressReleaseDetails.fragment = graphql(`
  fragment PressReleaseDetailsFragment on PressRelease {
    title
    author
    publishDate {
      default
    }
    coverImage {
      url
    }
    content {
      safeHtml
    }
  }
`)

export function PressReleaseDetails({ data }: { data: FragmentOf<typeof PressReleaseDetails.fragment> }) {
  const { t } = useEbanaLocale()

  const { title, author, publishDate, coverImage, content } = readFragment(PressReleaseDetails.fragment, data)
  return (
    <Box py='5em'>
      <Center flexDirection='column'>
        <>
          <VStack mb='6rem' gap='2.5rem'>
            <Text fontWeight={600} color='#00BFB2'>
              {t('news')}
            </Text>
            <Text dir='auto' fontWeight='bold' fontSize='2.25rem' lineHeight='2.875rem' color='#323B41'>
              {title}
            </Text>

            <Flex dir='auto' alignItems='center' gap='1em'>
              <ProfileImage name={author} />

              <VStack gap={0}>
                <Text fontWeight={600} fontSize='1.125rem' lineHeight='1.75rem'>
                  {author}
                </Text>
                <Text color='#4E5D78' fontSize='1rem' lineHeight='1.5rem'>
                  {publishDate?.default}
                </Text>
              </VStack>
            </Flex>

            <Image
              borderRadius='0.275em'
              border='1px solid #9D9EAB70'
              h={{ sm: null, md: '30rem' }}
              w={{ sm: null, md: '55rem' }}
              src={coverImage?.url}
              objectFit='contain'
              alt=''
            />
          </VStack>
        </>
        <DangerousHtml html={content.safeHtml} w={{ sm: null, md: '45rem' }} dir='auto' />
      </Center>
    </Box>
  )
}

function ProfileImage({ name }) {
  const nameParts = name.split(' ')
  const firstNameInitial = nameParts[0] ? nameParts[0][0] : ''
  const lastNameInitial = nameParts[1] ? nameParts[1][0] : ''

  return (
    <Box
      display='flex'
      justifyContent='center'
      alignItems='center'
      width='1.8em'
      height='1.8em'
      backgroundColor='#3C486B'
      color='#ffffff'
      fontSize='1.8em'
      fontWeight='800'
      borderRadius='50%'>
      {firstNameInitial}
      {lastNameInitial}
    </Box>
  )
}
