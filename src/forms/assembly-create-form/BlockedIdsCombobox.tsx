import { EbanaComboboxField2 } from "@/components/form/EbanaComboboxField2"
import { CompanyContext } from "@/context/company"
import { graphql } from '@/graphql'
import { Checkbox } from "@chakra-ui/react"
import React from "react"
import { useQuery } from 'urql'

const BlockedIdsOptionQuery = graphql(`
    query BlockedIdsOptionQuery($id: ID!, $search: String) {
      company(id: $id) {
        assembly_shareholder_options(search: $search) {
          id {
            type
            value
            inputType
          }
          name
        }
      }
    }
  `)

export function BlockedIdsCombobox({ field }) {
    const [search, setSearch] = React.useState('')

    const { id } = React.useContext(CompanyContext)

    const [{ data }] = useQuery({
        query: BlockedIdsOptionQuery,
        variables: {
            id,
            search,
        },
    })

    return (
        <EbanaComboboxField2
            field={field}
            options={data?.company.assembly_shareholder_options?.map((it) => {
                return {
                    label: it.name,
                    value: { value: it.id.value, type: it.id.inputType },
                }
            })}
            renderOption={({ isSelected, data }) => {
                return (
                    <Checkbox.Root checked={isSelected} gap='0.5em'>
                        <Checkbox.HiddenInput />
                        <Checkbox.Control />
                        <Checkbox.Label>{data.label}</Checkbox.Label>
                    </Checkbox.Root>
                )
            }}
            equalityFn={(a, b) => a.value === b.value}
            onInputChange={(v) => setSearch(v)}
            renderCreatable={(v) => v.value}
            renderTag={(props) => props.data.label}
        />
    )
}