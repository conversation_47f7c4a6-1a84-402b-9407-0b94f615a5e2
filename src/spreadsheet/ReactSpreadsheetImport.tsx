import merge from 'lodash/merge'

import { createContext } from 'react'
import { ModalWrapper } from './components/ModalWrapper'
import { Steps } from './steps/Steps'
import { themeOverrides } from './theme'
import { translations } from './translationsRSIProps'
import type { RsiProps } from './types'

export const defaultTheme = themeOverrides

export const RsiContext = createContext({} as any)

export const defaultRSIProps: Partial<RsiProps<any>> = {
  autoMapHeaders: true,
  autoMapSelectValues: false,
  allowInvalidSubmit: true,
  autoMapDistance: 2,
  isNavigationEnabled: false,
  translations: translations,
  uploadStepHook: async (value) => value,
  selectHeaderStepHook: async (headerValues, data) => ({ headerValues, data }),
  matchColumnsStepHook: async (table) => table,
  dateFormat: 'yyyy-mm-dd', // ISO 8601,
  parseRaw: true,
} as const

export const ReactSpreadsheetImport = <T extends string>(propsWithoutDefaults: RsiProps<T>) => {
  const { lng } = window.location.pathname.includes('ar') ? 'ar' : ('en' as any)

  const props = merge({}, defaultRSIProps, propsWithoutDefaults)
  const mergedTranslations =
    props.translations !== translations ? merge(translations, props.translations) : translations

  return (
    <RsiContext.Provider value={{ ...props, translations: mergedTranslations }}>
      <ModalWrapper open={props.open} setOpen={props.setOpen}>
        <Steps />
      </ModalWrapper>
    </RsiContext.Provider>
  )
}
