import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ger,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogRoot,
} from '@/components/ui/dialog'
import { Button } from '@chakra-ui/react'
import { useRef } from 'react'
import { useRsi } from '../../hooks/useRsi'

interface Props {
  open: boolean
  setOpen: (open: boolean) => void
  onConfirm: () => void
}

export const ConfirmCloseAlert = ({ open, setOpen, onConfirm }: Props) => {
  const { translations } = useRsi()
  const cancelRef = useRef<HTMLButtonElement | null>(null)

  return (
    <DialogRoot open={open}>
      <DialogContent>
        <DialogCloseTrigger />
        <DialogHeader fontSize='lg' fontWeight='bold'>
          {translations.alerts.confirmClose.headerTitle}
        </DialogHeader>
        <DialogBody>{translations.alerts.confirmClose.bodyText}</DialogBody>
        <DialogFooter>
          <Button ref={cancelRef} onClick={() => setO<PERSON>(false)} variant='secondary'>
            {translations.alerts.confirmClose.cancelButtonTitle}
          </Button>
          <Button colorScheme='red' onClick={onConfirm} ml={3}>
            {translations.alerts.confirmClose.exitButtonTitle}
          </Button>
        </DialogFooter>
      </DialogContent>
    </DialogRoot>
  )
}
