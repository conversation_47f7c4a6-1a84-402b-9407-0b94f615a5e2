import { <PERSON><PERSON><PERSON><PERSON>, Di<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, Di<PERSON><PERSON>eader, DialogRoot } from '@/components/ui/dialog'
import { Button } from '@chakra-ui/react'
import { useRef } from 'react'
import { useRsi } from '../../hooks/useRsi'

interface Props {
  open: boolean
  setOpen: (open: boolean) => void
  onConfirm: () => void
}

export const SubmitDataAlert = ({ open, setOpen, onConfirm }: Props) => {
  const { allowInvalidSubmit, translations } = useRsi()
  const cancelRef = useRef<HTMLButtonElement | null>(null)

  return (
    <DialogRoot open={open} onOpenChange={({ open }) => setOpen(open)} id='rsi'>
      <DialogContent>
        <DialogHeader fontSize='lg' fontWeight='bold'>
          {translations.alerts.submitIncomplete.headerTitle}
        </DialogHeader>
        <DialogBody>
          {allowInvalidSubmit
            ? translations.alerts.submitIncomplete.bodyText
            : translations.alerts.submitIncomplete.bodyTextSubmitForbidden}
        </DialogBody>
        <DialogFooter>
          <Button ref={cancelRef} onClick={() => setOpen(false)} variant='secondary'>
            {translations.alerts.submitIncomplete.cancelButtonTitle}
          </Button>
          {allowInvalidSubmit && (
            <Button onClick={onConfirm} ml={3}>
              {translations.alerts.submitIncomplete.finishButtonTitle}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </DialogRoot>
  )
}
