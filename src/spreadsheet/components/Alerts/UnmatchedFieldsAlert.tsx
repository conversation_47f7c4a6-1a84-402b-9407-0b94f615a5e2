import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogRoot } from '@/components/ui/dialog'
import { Box, Button, Text } from '@chakra-ui/react'
import { useRef } from 'react'
import { useRsi } from '../../hooks/useRsi'

interface Props {
  open: boolean
  setOpen: (open: boolean) => void
  onConfirm: () => void
  fields: string[]
}

export const UnmatchedFieldsAlert = ({ open, setOpen, onConfirm, fields }: Props) => {
  const { allowInvalidSubmit, translations } = useRsi()
  const cancelRef = useRef<HTMLButtonElement | null>(null)

  return (
    <DialogRoot open={open} onOpenChange={({ open }) => setOpen(open)} id='rsi'>
      <DialogContent>
        <DialogHeader fontSize='lg' fontWeight='bold'>
          {translations.alerts.unmatchedRequiredFields.headerTitle}
        </DialogHeader>
        <DialogBody>
          {translations.alerts.unmatchedRequiredFields.bodyText}
          <Box pt={3}>
            <Text display='inline'>{translations.alerts.unmatchedRequiredFields.listTitle}</Text>
            <Text display='inline' fontWeight='bold'>
              {fields.join(', ')}
            </Text>
          </Box>
        </DialogBody>
        <DialogFooter>
          <Button ref={cancelRef} onClick={() => setOpen(false)} variant='secondary'>
            {translations.alerts.unmatchedRequiredFields.cancelButtonTitle}
          </Button>
          {allowInvalidSubmit && (
            <Button onClick={onConfirm} ml={3}>
              {translations.alerts.unmatchedRequiredFields.continueButtonTitle}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </DialogRoot>
    // <AlertDialog isOpen={isOpen} onClose={onClose} leastDestructiveRef={cancelRef} isCentered id='rsi'>
    //   <AlertDialogOverlay>
    //     <AlertDialogContent>
    //       <AlertDialogHeader fontSize='lg' fontWeight='bold'>
    //         {translations.alerts.unmatchedRequiredFields.headerTitle}
    //       </AlertDialogHeader>
    //       <AlertDialogBody>
    //         {translations.alerts.unmatchedRequiredFields.bodyText}
    //         <Box pt={3}>
    //           <Text display='inline'>{translations.alerts.unmatchedRequiredFields.listTitle}</Text>
    //           <Text display='inline' fontWeight='bold'>
    //             {' '}
    //             {fields.join(', ')}
    //           </Text>
    //         </Box>
    //       </AlertDialogBody>
    //       <AlertDialogFooter>
    //         <Button ref={cancelRef} onClick={onClose} variant='secondary'>
    //           {translations.alerts.unmatchedRequiredFields.cancelButtonTitle}
    //         </Button>
    //         {allowInvalidSubmit && (
    //           <Button onClick={onConfirm} ml={3}>
    //             {translations.alerts.unmatchedRequiredFields.continueButtonTitle}
    //           </Button>
    //         )}
    //       </AlertDialogFooter>
    //     </AlertDialogContent>
    //   </AlertDialogOverlay>
    // </AlertDialog>
  )
}
