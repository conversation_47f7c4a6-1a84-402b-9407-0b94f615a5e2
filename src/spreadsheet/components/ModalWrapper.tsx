import { <PERSON>alog<PERSON>ody, Dialog<PERSON>lose<PERSON>rigger, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogRoot } from '@/components/ui/dialog'
import React from 'react'

type Props = {
  children: React.ReactNode
  open: boolean
  setOpen: (open: boolean) => void
}

export const ModalWrapper = ({ children, open, setOpen }: Props) => {
  return (
    <DialogRoot
      open={open}
      onOpenChange={({ open }: { open: boolean }) => setOpen(open)}
      id='rsi'
      // variant='rsi'
      closeOnEscape={false}
      closeOnInteractOutside={false}
      size='full'
      scrollBehavior='inside'>
      <DialogContent>
        <DialogCloseTrigger />
        <DialogHeader padding='0 0 24px 0' />
        <DialogBody padding='0'>{children}</DialogBody>
      </DialogContent>
    </DialogRoot>
  )
}
