import { Box, Popover } from '@chakra-ui/react'
import React from 'react'
import { useRsi } from '../../hooks/useRsi'

export const SELECT_DROPDOWN_ID = 'react-select-dropdown-wrapper'

type MenuPortalProps = {
  controlElement: React.ReactNode
  children: React.ReactNode
}

export const MenuPortal = ({ controlElement, children }: MenuPortalProps) => {
  const { rtl } = useRsi()

  return (
    <Popover.Root positioning={{ placement: 'top' }} lazyMount>
      <Popover.Trigger>
        <Box as='span'>{controlElement}</Box>
      </Popover.Trigger>
      <Popover.Content dir={rtl ? 'rtl' : 'ltr'} id='chakra-modal-rsi' width='auto'>
        {children}
      </Popover.Content>
    </Popover.Root>
  )
}

export const customComponents = {
  MenuPortal,
}
