import DataGrid from 'react-data-grid'
import { useRsi } from '../hooks/useRsi'

export const Table = (props) => {
  const { className, rows, columns, ...rest } = props

  const { rtl } = useRsi()

  if (!columns || !rows) return

  return (
    <DataGrid
      className={'rdg-light ' + className || ''}
      direction={rtl ? 'rtl' : 'ltr'}
      rows={rows}
      columns={columns}
      rowsCount={rows.length}
    />
  )
}
