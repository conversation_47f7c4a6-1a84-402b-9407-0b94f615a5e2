import { useMemo } from 'react'
import { Table } from '../../../components/Table'
import type { RawData } from '../../../types'
import { generateSelectionColumns } from './columns'

interface Props {
  data: RawData[]
}

export const SelectHeaderTable = ({ data }: Props) => {
  const columns = useMemo(() => generateSelectionColumns(data), [data])

  return <Table rows={data} columns={columns} headerRowHeight={0} />
}
