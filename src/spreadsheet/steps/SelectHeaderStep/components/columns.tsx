import { Radio, RadioGroup } from '@/components/ui/radio'
import React from 'react'
import { Column, useRowSelection } from 'react-data-grid'
import type { RawData } from '../../../types'

const SELECT_COLUMN_KEY = 'select-row'

function SelectFormatter({ row }: { row: any }) {
  const { isRowSelectionDisabled, isRowSelected, onRowSelectionChange } = useRowSelection()

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const nativeEvent = event.nativeEvent as MouseEvent
    onRowSelectionChange({
      row,
      checked: event.target.checked,
      isShiftClick: nativeEvent.shiftKey,
    })
  }

  return (
    <RadioGroup>
      <Radio
        aria-label='Select'
        value='selected'
        inputProps={{
          checked: isRowSelected,
          disabled: isRowSelectionDisabled,
          onChange: handleChange,
        }}
      />
    </RadioGroup>
  )
}

export const SelectColumn: Column<any> = {
  key: SELECT_COLUMN_KEY,
  name: '',
  width: 35,
  minWidth: 35,
  maxWidth: 35,
  resizable: false,
  sortable: false,
  frozen: true,
  cellClass: 'rdg-radio',
  renderCell: SelectFormatter,
}

export const generateSelectionColumns = (data: RawData[]) => {
  const longestRowLength = data.reduce((acc, curr) => (acc > curr.length ? acc : curr.length), 0)
  return [
    SelectColumn,
    ...Array.from({ length: longestRowLength }, (_, index) => ({
      key: index.toString(),
      name: '',
    })),
  ]
}
