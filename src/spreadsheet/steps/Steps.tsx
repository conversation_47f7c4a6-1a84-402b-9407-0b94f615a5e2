import { Cg<PERSON>heck } from 'react-icons/cg'
import { StepState, StepType, UploadFlow } from './UploadFlow'

import EbanaStepper from '@/components/EbanaStepper'
import { Dialog } from '@chakra-ui/react'
import React, { useRef, useState } from 'react'
import { useRsi } from '../hooks/useRsi'
import { stepIndexToStepType, steps, stepTypeToStepIndex } from '../utils/steps'

const CheckIcon = ({ color }: { color: string }) => (
  <>
    <CgCheck size='36px' color={color} />
  </>
)

export const Steps = () => {
  const { initialStepState, translations, isNavigationEnabled } = useRsi()

  const initialStep = stepTypeToStepIndex(initialStepState?.type)

  const [activeStep, setActiveStep] = React.useState(1)

  const [state, setState] = useState<StepState>(initialStepState || { type: StepType.upload })

  const history = useRef<StepState[]>([])

  const onClickStep = (stepIndex: number) => {
    const type = stepIndexToStepType(stepIndex)
    const historyIdx = history.current.findIndex((v) => v.type === type)
    if (historyIdx === -1) return
    const nextHistory = history.current.slice(0, historyIdx + 1)
    history.current = nextHistory
    setState(nextHistory[nextHistory.length - 1])
    setActiveStep(stepIndex)
  }

  const onBack = () => {
    onClickStep(Math.max(activeStep - 1, 0))
  }

  const onNext = (v: StepState) => {
    history.current.push(state)
    setState(v)
    v.type !== StepType.selectSheet && setActiveStep(activeStep + 1)
  }

  return (
    <>
      <Dialog.Header display={['none', 'none', 'block']}>
        <EbanaStepper steps={steps.map((description) => ({ description }))} activeStep={activeStep} />
      </Dialog.Header>
      <UploadFlow state={state} onNext={onNext} onBack={isNavigationEnabled ? onBack : undefined} />
    </>
  )
}
