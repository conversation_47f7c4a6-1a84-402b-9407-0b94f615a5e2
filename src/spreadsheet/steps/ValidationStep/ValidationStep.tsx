import { toaster } from '@/components/ui/toaster'
import { Box, Dialog, Heading, Switch } from '@chakra-ui/react'
import { useCallback, useMemo, useState } from 'react'
import { SubmitDataAlert } from '../../components/Alerts/SubmitDataAlert'
import { ContinueButton } from '../../components/ContinueButton'
import { Table } from '../../components/Table'
import { useRsi } from '../../hooks/useRsi'
import type { Data } from '../../types'
import { generateColumns } from './components/columns'
import type { Meta } from './types'
import { addErrorsAndRunHooks } from './utils/dataMutations'

type Props<T extends string> = {
  initialData: (Data<T> & Meta)[]
  file: File
  onBack?: () => void
}

export const ValidationStep = <T extends string>({ initialData, file, onBack }: Props<T>) => {
  const { translations, fields, setOpen, onSubmit, rowHook, tableHook } = useRsi<T>()

  const [data, setData] = useState<(Data<T> & Meta)[]>(initialData)

  const [selectedRows, setSelectedRows] = useState<ReadonlySet<number | string>>(new Set())
  const [filterByErrors, setFilterByErrors] = useState(false)
  const [showSubmitAlert, setShowSubmitAlert] = useState(false)
  const [isSubmitting, setSubmitting] = useState(false)

  const updateData = useCallback(
    async (rows: typeof data, indexes?: number[]) => {
      // Check if hooks are async - if they are we want to apply changes optimistically for better UX
      if (rowHook?.constructor.name === 'AsyncFunction' || tableHook?.constructor.name === 'AsyncFunction') {
        setData(rows)
      }
      addErrorsAndRunHooks<T>(rows, fields, rowHook, tableHook, indexes).then((data) => setData(data))
    },
    [rowHook, tableHook, fields]
  )

  const deleteSelectedRows = () => {
    if (selectedRows.size) {
      const newData = data.filter((value) => !selectedRows.has(value.__index))
      updateData(newData)
      setSelectedRows(new Set())
    }
  }

  const columns = useMemo(() => generateColumns(fields), [fields])

  const tableData = useMemo(() => {
    if (filterByErrors) {
      return data.filter((value) => {
        if (value?.__errors) {
          return Object.values(value.__errors)?.filter((err) => err.level === 'error').length
        }
        return false
      })
    }
    return data
  }, [data, filterByErrors])

  const submitData = async () => {
    const calculatedData = data.reduce(
      (acc, value) => {
        const { __index, __errors, ...values } = value
        if (__errors) {
          for (const key in __errors) {
            if (__errors[key].level === 'error') {
              acc.invalidData.push(values as unknown as Data<T>)
              return acc
            }
          }
        }
        acc.validData.push(values as unknown as Data<T>)
        return acc
      },
      { validData: [] as Data<T>[], invalidData: [] as Data<T>[], all: data }
    )
    setShowSubmitAlert(false)
    setSubmitting(true)
    const response = onSubmit(calculatedData, file)
    if (response && response.then) {
      // TypeScript will now know `response` is a Promise
      response
        .then(() => {
          setOpen(false)
        })
        .catch((err: Error) => {
          toaster.create({
            type: 'error',
            title: `${translations.alerts.submitError.title}`,
            description: err?.message || `${translations.alerts.submitError.defaultMessage}`,
            closable: true,
          })
        })
        .finally(() => {
          setSubmitting(false)
        })
    } else {
      setOpen(false)
    }
  }
  const onContinue = () => {
    const invalidData = data.find((value) => {
      if (value?.__errors) {
        return !!Object.values(value.__errors)?.filter((err) => err.level === 'error').length
      }
      return false
    })
    if (!invalidData) {
      submitData()
    } else {
      setShowSubmitAlert(true)
    }
  }

  return (
    <>
      {/* todo: fix setOpen */}
      <SubmitDataAlert open={showSubmitAlert} setOpen={() => setShowSubmitAlert(false)} onConfirm={submitData} />
      <Dialog.Body pb={0}>
        <Box display='flex' justifyContent='space-between' alignItems='center' mb='2rem' flexWrap='wrap' gap='8px'>
          <Heading
            css={{
              color: 'textColor',
              fontSize: '3xl',
            }}>
            {translations.validationStep.title}
          </Heading>
          <Switch.Root
            display='flex'
            // alignItems='center'
            checked={filterByErrors}
            onChange={() => setFilterByErrors(!filterByErrors)}>
            <Switch.HiddenInput />
            <Switch.Control />
            <Switch.Label> {translations.validationStep.filterSwitchTitle}</Switch.Label>
          </Switch.Root>
        </Box>
        <Table rows={tableData} columns={columns} />
      </Dialog.Body>
      <ContinueButton
        isLoading={isSubmitting}
        onContinue={onContinue}
        onBack={onBack}
        title={translations.validationStep.nextButtonTitle}
        backTitle={translations.validationStep.backButtonTitle}
      />
    </>
  )
}
