import { Checkbox } from '@/components/ui/checkbox'
import { Tooltip } from '@/components/ui/tooltip'
import { Box, Input, Switch } from '@chakra-ui/react'
import type { ChangeEvent } from 'react'
import { Column, useRowSelection } from 'react-data-grid'
import { CgInfo } from 'react-icons/cg'
import { TableSelect } from '../../../components/Selects/TableSelect'
import type { Data, Fields } from '../../../types'
import type { Meta } from '../types'

const SELECT_COLUMN_KEY = 'select-row'

function autoFocusAndSelect(input: HTMLInputElement | null) {
  input?.focus()
  input?.select()
}

function SelectFormatter({ row }: { row: any }) {
  const { isRowSelectionDisabled, isRowSelected, onRowSelectionChange } = useRowSelection()

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const nativeEvent = event.nativeEvent as MouseEvent
    onRowSelectionChange({
      row,
      checked: event.target.checked,
      isShiftClick: nativeEvent.shiftKey,
    })
  }

  return (
    <Checkbox
      aria-label='Select'
      disabled={isRowSelectionDisabled}
      inputProps={{
        checked: isRowSelected,
        onChange: handleChange,
      }}
    />
  )
}

export const generateColumns = <T extends string>(fields: Fields<T>): Column<Data<T> & Meta>[] => [
  {
    key: SELECT_COLUMN_KEY,
    name: '',
    width: 35,
    minWidth: 35,
    maxWidth: 35,
    resizable: false,
    sortable: false,
    frozen: true,
    cellClass: 'rdg-checkbox',
    renderCell: SelectFormatter,
  },
  ...fields.map(
    (column): Column<Data<T> & Meta> => ({
      key: column.key,
      name: column.label,
      minWidth: 150,
      resizable: true,
      renderHeaderCell: () => (
        <Box display='flex' gap={1} alignItems='center' position='relative'>
          <Box flex={1} overflow='hidden' textOverflow='ellipsis'>
            {column.label}
          </Box>
          {column.description && (
            <Tooltip
              positioning={{ placement: 'top' }}
              showArrow
              aria-label={column.description}
              content={
                <Box flex='0 0 auto'>
                  <CgInfo size='16px' />
                </Box>
              }
            />
          )}
        </Box>
      ),
      editable: column.fieldType.type !== 'checkbox',
      editor: ({ row, onRowChange, onClose }) => {
        let component

        switch (column.fieldType.type) {
          case 'select':
            component = (
              <TableSelect
                value={column.fieldType.options.find((option) => option.value === (row[column.key] as string))}
                onChange={(value) => {
                  onRowChange({ ...row, [column.key]: value?.value }, true)
                }}
                options={column.fieldType.options}
              />
            )
            break
          default:
            component = (
              <Box paddingInlineStart='0.5rem'>
                <Input
                  ref={autoFocusAndSelect}
                  autoFocus
                  value={row[column.key] as string}
                  onChange={(event: ChangeEvent<HTMLInputElement>) => {
                    onRowChange({ ...row, [column.key]: event.target.value })
                  }}
                  onBlur={() => onClose(true)}
                />
              </Box>
            )
        }

        return component
      },
      // editorOptions: {
      //   editOnClick: true,
      // },
      formatter: ({ row, onRowChange }) => {
        let component

        switch (column.fieldType.type) {
          case 'checkbox':
            component = (
              <Box
                display='flex'
                alignItems='center'
                height='100%'
                onClick={(event) => {
                  event.stopPropagation()
                }}>
                <Switch.Root
                  checked={row[column.key] as boolean}
                  onChange={() => {
                    onRowChange({ ...row, [column.key]: !row[column.key as T] })
                  }}>
                  <Switch.HiddenInput />
                </Switch.Root>
              </Box>
            )
            break
          case 'select':
            component = (
              <Box minWidth='100%' minHeight='100%' overflow='hidden' textOverflow='ellipsis'>
                {column.fieldType.options.find((option) => option.value === row[column.key as T])?.label || null}
              </Box>
            )
            break
          default:
            component = (
              <Box minWidth='100%' minHeight='100%' overflow='hidden' textOverflow='ellipsis'>
                {row[column.key as T]}
              </Box>
            )
        }

        if (row.__errors?.[column.key]) {
          return (
            <Tooltip
              positioning={{ placement: 'top' }}
              showArrow
              aria-label={row.__errors?.[column.key]?.message}
              content={component}
            />
          )
        }

        return component
      },
      cellClass: (row: Meta) => {
        switch (row.__errors?.[column.key]?.level) {
          case 'error':
            return 'rdg-cell-error'
          case 'warning':
            return 'rdg-cell-warning'
          case 'info':
            return 'rdg-cell-info'
          default:
            return ''
        }
      },
    })
  ),
]
