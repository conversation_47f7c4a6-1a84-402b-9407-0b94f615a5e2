enum StepType {
  upload = 'upload',
  selectSheet = 'selectSheet',
  selectHeader = 'selectHeader',
  matchColumns = 'matchColumns',
  validateData = 'validateData',
}

export const steps = [
  'uploadStep',
  'selectSheetStep',
  'selectHeaderStep',
  'matchColumnsStep',
  'validationStep',
] as const

const StepTypeToStepRecord: Record<StepType, (typeof steps)[number]> = {
  [StepType.upload]: 'uploadStep',
  [StepType.selectSheet]: 'selectSheetStep',
  [StepType.selectHeader]: 'selectHeaderStep',
  [StepType.matchColumns]: 'matchColumnsStep',
  [StepType.validateData]: 'validationStep',
}
const StepToStepTypeRecord: Record<(typeof steps)[number], StepType> = {
  uploadStep: StepType.upload,
  selectSheetStep: StepType.selectSheet,
  selectHeaderStep: StepType.selectHeader,
  matchColumnsStep: StepType.matchColumns,
  validationStep: StepType.validateData,
}

export const stepIndexToStepType = (stepIndex: number) => {
  const step = steps[stepIndex]
  return StepToStepTypeRecord[step] || StepType.upload
}

export const stepTypeToStepIndex = (type?: StepType) => {
  const step = StepTypeToStepRecord[type || StepType.upload]
  return Math.max(0, steps.indexOf(step))
}
